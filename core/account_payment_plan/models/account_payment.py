from odoo import _, api, fields, models
from odoo.exceptions import UserError, ValidationError
from datetime import datetime


class AccountPayment(models.Model):
    _inherit = "account.payment"

    payment_plan_id = fields.Many2one(comodel_name="account.payment.plan", string="Payment Plan")
    payment_plans_count = fields.Integer(compute='_payment_plans_count', string='Payment Plans Count')
    payment_invoice_ids = fields.One2many('account.payment.invoice', 'payment_id', 'Invoices')
    amount_payment_invoice = fields.Monetary(currency_field='currency_id', string="Total", compute='_set_amount_payment_invoice')

    def open_payment_plans(self):
        return {
            'name': _('Payment Plan'),
            'view_mode': 'list,form',
            'res_model': 'account.payment.plan',
            'views': [(self.env.ref('account_payment_plan.view_payment_plan_tree').id, 'list'), (False, 'form')],
            'type': 'ir.actions.act_window',
            'domain': [('id', '=', self.payment_plan_id.id)],
            'context': dict(self._context, create=False),
        }

    def _payment_plans_count(self):
        for rec in self:
            res = self.env['account.payment.plan'].search_count([('id', '=', rec.payment_plan_id.id)])
            rec.payment_plans_count = res or 0

    @api.model
    def create(self, vals):
        res = super(AccountPayment, self).create(vals)
        for payment in res.payment_invoice_ids:
            if res.date and payment.date_invoice:
                if res.date < payment.date_invoice:
                    raise ValidationError('Payment date tidak boleh lebih kecil dari Bill date')
        return res

    def write(self, vals):
        res = super(AccountPayment, self).write(vals)
        for payment in self.payment_invoice_ids:
            if self.date and payment.date_invoice:
                if self.date < payment.date_invoice:
                    raise ValidationError('Payment date tidak boleh lebih kecil dari Bill date')
        return res

    @api.depends('payment_invoice_ids')
    def _set_amount_payment_invoice(self):
        self.amount_payment_invoice = sum(self.payment_invoice_ids.mapped('amount'))

