from odoo import api, fields, models
from odoo.exceptions import ValidationError


class AccountPaymentInvoice(models.Model):
    _name = 'account.payment.invoice'
    _description = 'Payment Invoice'

    payment_id = fields.Many2one('account.payment', 'Payment', required=True)
    name = fields.Char('Invoice No.')
    payment_reference = fields.Char('Payment Reference.')
    date_invoice = fields.Date('Bill Date')
    date_accounting = fields.Date('GL Date')
    account_id = fields.Many2one('account.account', 'Account', compute='_compute_account_id')
    description = fields.Char('Description')
    po_number = fields.Char('PO Number')
    currency_id = fields.Many2one('res.currency', 'Currency')
    amount = fields.Float('Amount')
    move_id = fields.Many2one('account.move', string='Invoices', required=True)
    invoice_state = fields.Selection(selection=[
        ('unapplied', 'Unapplied'),
        ('applied', 'Applied'),
    ], string='Status', default='unapplied', compute='_compute_invoice_state')

    @api.depends('payment_id.state', 'move_id.state')
    def _compute_invoice_state(self):
        for invoice in self:
            # print(invoice.payment_id.state, 'ini apa statusnya ??')
            # if invoice.payment_id.state != 'paid' or (invoice.payment_id.reverse_date and invoice.payment_id.state == 'paid'):
            if invoice.payment_id.state != 'paid':
                invoice.invoice_state = 'unapplied'
                if invoice.move_id.line_ids:
                    if invoice.move_id.line_ids:
                        for line in invoice.move_id.line_ids:
                            if invoice.move_id.amount_residual_signed != 0 and line.reconciled is False:
                                line.payment_id = None
                                invoice.invoice_state = 'unapplied'
                            elif line.reconciled is True:
                                invoice.invoice_state = 'unapplied'
                                line.remove_move_reconcile()
                            else:
                                invoice.invoice_state = 'unapplied'
                    else:
                        invoice.invoice_state = 'unapplied'
                else:
                    invoice.invoice_state = 'unapplied'
                invoice.invoice_state = 'unapplied'
                # if invoice.move_id.amount_residual_signed != 0:
                #     if abs(invoice.move_id.amount_residual_signed) == abs(invoice.move_id.amount_total_signed):
                #         invoice.move_id.payment_state = 'not_paid'
                #     else:
                #         invoice.move_id.payment_state = 'partial'
                # elif invoice.move_id.amount_residual_signed == 0:
                #     invoice.move_id.payment_state = 'in_payment'
                # else:
                #     invoice.move_id.payment_state = 'not_paid'
            # elif invoice.payment_id.state == 'paid':
            #     invoice.invoice_state = 'applied'
            #     if invoice.move_id.amount_residual_signed != 0:
            #         if abs(invoice.move_id.amount_residual_signed) == abs(invoice.move_id.amount_total_signed):
            #             invoice.move_id.payment_state = 'not_paid'
            #         else:
            #             invoice.move_id.payment_state = 'partial'
            #     elif invoice.move_id.amount_residual_signed == 0:
            #         # invoice.move_id.payment_state = 'in_payment'
            #         for payment in invoice.payment_id.move_id.line_ids:
            #             if payment.reconciled is True:
            #                 if invoice.payment_id.destination_account_id != payment.account_id:
            #                     invoice.move_id.payment_state = 'in_payment'
            #                 # elif invoice.payment_id.date_bank_statement is False:
            #                 #     invoice.move_id.payment_state = 'in_payment'
            #                 else:
            #                     invoice.move_id.payment_state = 'paid'
            #             else:
            #                 invoice.move_id.payment_state = 'in_payment'
            #     else:
            #         invoice.move_id.payment_state = 'not_paid'
            else:
                invoice.invoice_state = 'unapplied'

    @api.onchange('move_id')
    def _onchange_set_values(self):
        for rec in self:
            if rec.move_id:
                move = self.env['account.move'].sudo().browse(rec.move_id.id)
                description = '%s' % (move.ref or '')
                rec.name = move.name
                rec.payment_reference = move.payment_reference or False
                rec.date_invoice = move.invoice_date
                rec.date_accounting = move.date
                rec.description = description
                # rec.po_number = move.po_numbers
                rec.currency_id = move.currency_id.id
                if rec.payment_id:
                    rec.amount = rec.payment_id.amount
                else:
                    rec.amount = move.amount_residual

    @api.depends('move_id.line_ids')
    def _compute_account_id(self):
        for rec in self:
            if rec.move_id:
                ln = rec.move_id.line_ids.filtered(lambda x: x.credit and x.account_id.account_type == 'liability_payable')
                account = ln.account_id
                rec.account_id = account.id or False
            else:
                rec.account_id = False

    @api.model
    def create(self, vals):
        res = super(AccountPaymentInvoice, self).create(vals)
        if res.payment_id and res.payment_id.date and res.date_invoice:
            if res.payment_id.date < res.date_invoice:
                raise ValidationError('Payment date tidak boleh lebih kecil dari Bill date')
        return res

    def write(self, vals):
        res = super(AccountPaymentInvoice, self).write(vals)
        if self.payment_id and self.payment_id.date and self.date_invoice:
            if self.payment_id.date < self.date_invoice:
                raise ValidationError('Payment date tidak boleh lebih kecil dari Bill date')
        return res
