# -*- coding: utf-8 -*-

from datetime import date, datetime

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class AccountRelocation(models.Model):
    _name = 'account.relocation'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    def _get_default_unit(self):
        unit = False
        if self.requestor_id:
            if self.requestor_id.department_id:
                department = self.requestor_id.department_id
                while department:
                    if department.department_type == '4_unit':
                        unit = self.requestor_id.department_id.id
                    department = department.parent_id
        return unit

    def _get_default_general_budget_ids(self):
        return self.env['account.budget.post'].search([('id', 'in', [])]).ids

    name = fields.Char('Budget Relocation Number', default='New', tracking=True)
    date = fields.Date('Transaction Date', tracking=True, default=datetime.today().date())
    year = fields.Char('Year', compute='_compute_year', store=True)
    employee_id = fields.Many2one('hr.employee', 'Submitter Name', tracking=True, default=lambda self: self.env.user.employee_id)
    requestor_id = fields.Many2one('hr.employee', 'Requestor Name', tracking=True, default=lambda self: self.env.user.employee_id)
    unit_id = fields.Many2one('hr.department', 'Unit', domain=[('department_type', '=', '4_unit')], tracking=True, context={'hierarchical_naming': False})
    group_id = fields.Many2one('hr.department', 'Group', domain=[('department_type', '=', '3_group')], tracking=True, context={'hierarchical_naming': False})
    directorate_id = fields.Many2one('hr.department', 'Direktorat', domain=[('department_type', '=', '1_dir')], tracking=True, context={'hierarchical_naming': False})
    period_program = fields.Char('Periode Program', tracking=True)
    budget_id = fields.Many2one('crossovered.budget', 'Budget', tracking=True)
    rkap_recipient_id = fields.Many2one('account.budget.post', 'RKAP Recipient', tracking=True)
    rkap_recipient_ids = fields.Many2many('account.budget.post', string='RKAP Recipient', compute='_compute_rkap_recipient_ids')
    company_id = fields.Many2one('res.company', 'Company', required=True,
        default=lambda self: self.env.company)
    company_currency_id = fields.Many2one('res.currency', 'Currency', default=lambda self: self.env.company.currency_id, tracking=True)
    rkap_budget_recipient_amount = fields.Monetary(currency_field='company_currency_id', string='RKAP Amount Budget Recipient', tracking=True, compute='_compute_rkap_budget_recipient_amount', store=True)
    total_budget_amount = fields.Monetary(currency_field='company_currency_id', string='Total Budget Donor', compute='_compute_total_budget_amount', store=True, tracking=True)
    ending_rkap_budget_recipient_amount = fields.Monetary(currency_field='company_currency_id', string='Ending RKAP Amount Budget Receipient', compute='_compute_rkap_budget_recipient_amount', store=True)
    attachment_id = fields.Many2many('ir.attachment', string='Relocation Attachment', tracking=True)
    line_ids = fields.One2many('account.relocation.line', 'justification_id', 'Relocation Lines', tracking=True)
    state = fields.Selection([
        ('draft', 'Draft'),
        ('pending_approval', 'Pending Approval'),
        ('approved', 'Approved'),
        ('return', 'Return'),
        ('rejected', 'Rejected'),
        ('cancel', 'Canceled'),
        ('done', 'Close'),
    ], string='Relocation State', default='draft', tracking=True)
    active = fields.Boolean('Active', default=True, tracking=True)
    period_id = fields.Many2one('crossovered.budget.period', string="Period")
    lt_period_id = fields.Many2one('crossovered.budget.period', string="Next Year Period")
    flag_lintas_tahun_active = fields.Boolean('Lintas Tahun')
    general_budget_ids = fields.Many2many('account.budget.post', string='Budgetary Position', compute='_compute_general_budget_ids', default=_get_default_general_budget_ids)

    def write(self, vals):
        for record in self:
            record.attachment_id.write({'res_model': self._name, 'res_id': record.id})
        return super(AccountRelocation, self).write(vals)

    @api.depends('period_id', 'rkap_recipient_id')
    def _compute_general_budget_ids(self):
        for rec in self:
            list_budgetary_position = []
            if rec.period_id:
                list_rkap_recipient = self.env['crossovered.budget.lines'].search([('crossovered_budget_id.period_id', '=', rec.period_id.id), ('crossovered_budget_id.state', '=', 'validate'), ('crossovered_budget_id.active', '=', True)])
                for line in list_rkap_recipient:
                    if rec.rkap_recipient_id.id != line.general_budget_id.id:
                        list_budgetary_position.append(line.general_budget_id.id)
            if list_budgetary_position != []:
                rec.general_budget_ids = [(6, 0, self.env['account.budget.post'].search([('id', 'in', list_budgetary_position)]).ids)]
            else:
                rec.general_budget_ids = [(6, 0, self.env['account.budget.post'].search([('id', 'in', [])]).ids)]

    @api.onchange('requestor_id')
    def _onchange_requestor(self):
        if not self.requestor_id:
            self.unit_id = False
            self.group_id = False
            self.directorate_id = False
            return
        department = self.requestor_id.department_id
        if not department:
            return
        if department.department_type == '4_unit':
            self.unit_id = department.id
            parent_group = department.parent_id
            if parent_group and parent_group.department_type == '3_group':
                self.group_id = parent_group.id
                parent_dir = parent_group.parent_id
                if parent_dir and parent_dir.department_type == '1_dir':
                    self.directorate_id = parent_dir.id
        elif department.department_type == '3_group':
            self.unit_id = False
            self.group_id = department.id
            parent_dir = department.parent_id
            if parent_dir and parent_dir.department_type == '1_dir':
                self.directorate_id = parent_dir.id
        elif department.department_type == '1_dir':
            self.unit_id = False
            self.group_id = False
            self.directorate_id = department.id

    @api.depends('period_id')
    def _compute_rkap_recipient_ids(self):
        for rec in self:
            list_rkap_recipient = []
            if rec.period_id:
                list_rkap_recipients = self.env['crossovered.budget.lines'].search([('crossovered_budget_id.period_id', '=', rec.period_id.id), ('crossovered_budget_id.state', '=', 'validate'), ('crossovered_budget_id.active', '=', True)])
                for line in list_rkap_recipients:
                    list_rkap_recipient.append(line.general_budget_id.id)
            rec.rkap_recipient_ids = [(6, 0, list_rkap_recipient)]

    @api.depends('period_id', 'rkap_recipient_id', 'budget_id', 'budget_id.crossovered_budget_line_add', 'rkap_budget_recipient_amount', 'line_ids', 'line_ids.relocation_amount', 'company_currency_id')
    def _compute_rkap_budget_recipient_amount(self):
        for rec in self:
            amount = 0
            # if rec.budget_id:
            #     for line in rec.budget_id.crossovered_budget_line_add:
            #         if rec.rkap_recipient_id:
            #             if line.general_budget_id.id == rec.rkap_recipient_id.id:
            #                 amount += line.amount_total_budget
            if rec.period_id and rec.rkap_recipient_id:
                list_rkap_recipient = self.env['crossovered.budget.lines'].search([('crossovered_budget_id.period_id', '=', rec.period_id.id), ('crossovered_budget_id.state', '=', 'validate'), ('crossovered_budget_id.active', '=', True), ('general_budget_id', '=', rec.rkap_recipient_id.id)])
                for line in list_rkap_recipient:
                    # amount += line.remaining_amount
                    amount += line.currency_id._convert(line.remaining_amount, self.company_currency_id, self.company_id, date=datetime.today())
            # if rec.rkap_recipient_id and amount <= 0:
            #     raise ValidationError('RKAP Amount Budget Recipient Harus Lebih Besar Dari 0')
            rec.rkap_budget_recipient_amount = amount
            rec.ending_rkap_budget_recipient_amount = rec.rkap_budget_recipient_amount + rec.total_budget_amount

    @api.model
    def create(self, vals):
        vals['name'] = self.env['ir.sequence'].next_by_code('account.relocation')
        res = super(AccountRelocation, self).create(vals)
        return res

    def write(self, vals):
        for record in self:
            record.attachment_id.write({'res_model': self._name, 'res_id': record.id})
        res = super(AccountRelocation, self).write(vals)
        return res

    def action_budget_approve(self):
        self.state = 'approved'
        if self.rkap_recipient_id:
            line_budget = self.env['crossovered.budget.lines'].search([('general_budget_id', '=', self.rkap_recipient_id.id), ('crossovered_budget_id.period_id', '=', self.period_id.id), ('crossovered_budget_id.state', '=', 'validate')])
            if line_budget:
                for line in self.line_ids:
                    target_budget = self.env['crossovered.budget.lines'].search([('general_budget_id', '=', line.general_budget_id.id), ('crossovered_budget_id.period_id', '=', self.period_id.id), ('crossovered_budget_id.state', '=', 'validate')])
                    if target_budget:
                        target_budget.write({'relocation_out_amount': target_budget.relocation_out_amount + line.relocation_amount})
                        line_budget.write({'relocation_in_amount': line_budget.relocation_in_amount + line.relocation_amount})

    def action_budget_done(self):
        self.state = 'done'

    def action_budget_return(self):
        self.state = 'return'

    def action_budget_draft(self):
        self.state = 'draft'

    def action_budget_cancel(self):
        if self.state == 'approved':
            if self.rkap_recipient_id:
                line_budget = self.env['crossovered.budget.lines'].search([('general_budget_id', '=', self.rkap_recipient_id.id), ('crossovered_budget_id.period_id', '=', self.period_id.id), ('crossovered_budget_id.state', '=', 'validate')])
                if line_budget:
                    for line in self.line_ids:
                        target_budget = self.env['crossovered.budget.lines'].search([('general_budget_id', '=', line.general_budget_id.id), ('crossovered_budget_id.period_id', '=', self.period_id.id), ('crossovered_budget_id.state', '=', 'validate')])
                        if target_budget:
                            target_budget.write({'relocation_out_amount': target_budget.relocation_out_amount - line.relocation_amount})
                            line_budget.write({'relocation_in_amount': line_budget.relocation_in_amount - line.relocation_amount})
        self.state = 'cancel'

    @api.depends('line_ids', 'line_ids.relocation_amount', 'company_currency_id')
    def _compute_total_budget_amount(self):
        for rec in self:
            total_budget_amount = 0
            for line in rec.line_ids:
                # total_budget_amount += line.relocation_amount
                total_budget_amount += line.company_currency_id._convert(line.relocation_amount, self.company_currency_id, self.company_id, date=datetime.today())
            rec.total_budget_amount = total_budget_amount

    @api.depends('date')
    def _compute_year(self):
        for rec in self:
            rec.year = str(rec.date.year) if rec.date else -1

    # @api.onchange('flag_lintas_tahun_active')
    # def onchange_flag_lintas_tahun_active(self):
    #     if self.flag_lintas_tahun_active:
    #         if self.period_id:
    #             lt_period_id = self.env['crossovered.budget.period'].search([('name', '=', str(int(self.period_id.name) + 1))], limit=1)
    #             self.lt_period_id = lt_period_id.id if lt_period_id else False

    # @api.onchange('company_currency_id')
    # def onchange_company_currency_id(self):
    #     for line in self.line_ids:
    #         line.relocation_amount = self._origin.company_currency_id._convert(line.relocation_amount, self.company_currency_id, self.company_id, date=datetime.today())
