# -*- coding: utf-8 -*-

from datetime import date, datetime

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class AccountRelocationLine(models.Model):
    _name = 'account.relocation.line'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    justification_id = fields.Many2one('account.relocation', 'Line Number', ondelete='cascade')
    no = fields.Integer(string='Line Number', compute='_compute_line_number', store=True)
    description = fields.Text('RKAP Name')
    general_budget_id = fields.Many2one('account.budget.post', 'RKAP Name')
    unit_id = fields.Many2one('hr.department', 'Unit', domain=[('department_type', '=', '4_unit')], tracking=True, context={'hierarchical_naming': False})
    group_id = fields.Many2one('hr.department', 'Group', domain=[('department_type', '=', '3_group')], tracking=True, context={'hierarchical_naming': False})
    period_program = fields.Char('Periode Program', related='justification_id.period_program', store=True)
    period_id = fields.Many2one('crossovered.budget.period', string="Period", related='justification_id.period_id', store=True)
    company_currency_id = fields.Many2one('res.currency', 'Currency', store=True, default=lambda self: self.env.company.currency_id)
    relocation_amount = fields.Monetary(currency_field='company_currency_id', string='Amount Relocation')

    @api.onchange('general_budget_id')
    def onchange_rkap_name(self):
        if self.general_budget_id:
            list_rkap_recipient = self.env['crossovered.budget.lines'].search(
                [('crossovered_budget_id.period_id', '=', self.justification_id.period_id.id),
                 ('crossovered_budget_id.state', '=', 'validate'), ('crossovered_budget_id.active', '=', True),
                 ('general_budget_id', '=', self.general_budget_id.id)])
            for rkap in list_rkap_recipient:
                self.group_id = rkap.group_id.id
            # self.unit_id = self.justification_id.unit_id.id

    @api.depends('justification_id.line_ids')
    def _compute_line_number(self):
        for justification in self.mapped('justification_id'):
            sequence = 1
            for line in justification.line_ids:
                line.no = sequence
                sequence += 1

    @api.onchange('relocation_amount')
    def constraint_relocation_amount(self):
        if self.general_budget_id and self.justification_id.period_id:
            list_rkap_recipient = self.env['crossovered.budget.lines'].search([('crossovered_budget_id.period_id', '=', self.justification_id.period_id.id), ('crossovered_budget_id.state', '=', 'validate'), ('crossovered_budget_id.active', '=', True), ('general_budget_id', '=', self.general_budget_id.id)])
            remaining_amount = 0
            for line in list_rkap_recipient:
                # remaining_amount += line.remaining_amount
                remaining_amount += line.currency_id._convert(line.remaining_amount, self.company_currency_id, self.justification_id.company_id, date=datetime.today())
            if remaining_amount < self.relocation_amount:
                raise ValidationError('Amount Relocation cannot be greater than Remaining Amount')

    @api.model
    def create(self, vals):
        res = super(AccountRelocationLine, self).create(vals)
        if res.relocation_amount <= 0:
            raise ValidationError('RKAP Amount Budget Donor Harus Lebih Besar Dari 0')
        return res

    def write(self, vals):
        res = super(AccountRelocationLine, self).write(vals)
        for record in self:
            if record.relocation_amount <= 0:
                raise ValidationError('RKAP Amount Budget Donor Harus Lebih Besar Dari 0')
        return res

