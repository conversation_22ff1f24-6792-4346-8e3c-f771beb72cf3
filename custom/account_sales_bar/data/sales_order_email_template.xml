<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="email_template_sales_order_confirmation" model="mail.template">
            <field name="name">Sales Order: Confirmation Email</field>
            <field name="model_id" ref="sale.model_sale_order"/>
            <field name="subject">{{ object.company_id.name }} Sales Order (Ref {{ object.name or 'n/a' }})</field>
            <field name="email_from">{{ (object.user_id.email_formatted or user.email_formatted) }}</field>
            <field name="partner_to">{{ object.partner_id.id }}</field>
            <field name="description">Sent automatically when a sales order is confirmed</field>
            <field name="body_html" type="html">
<div style="margin: 0px; padding: 0px;">
    <p style="margin: 0px; padding: 0px; font-size: 13px;">
        Dear <t t-out="object.create_uid.name or ''"><PERSON></t>
        <br/><br/>
        Thank you for your order.
        <br/>
        Here is your <strong>Sales Order</strong> <strong t-out="object.name or ''">SOL/00004</strong>
        <br/>
    </p>
    <br/>
    <p style="margin: 0px; padding: 0px; font-size: 13px;">
        Do not hesitate to contact us if you have any questions.
        <br/>
    </p>
    <br/>
    <p style="margin: 0px; padding: 0px; font-size: 13px;">
        Best regards,
        <br/>
        <t t-if="object.user_id and object.user_id.signature">
            <t t-out="object.user_id.signature or ''">--<br/>Mitchell Admin</t>
        </t>
        <t t-else="1">
            --<br/><t t-out="object.company_id.name or ''">YourCompany</t>
        </t>
    </p>
</div>
            </field>
            <field name="lang">{{ object.partner_id.lang }}</field>
            <field name="auto_delete" eval="True"/>
        </record>
    </data>
</odoo>