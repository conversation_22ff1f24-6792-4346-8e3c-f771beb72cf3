from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError
import xml.etree.ElementTree as ET
import xml.dom.minidom
from markupsafe import escape, Markup


import logging
_logger = logging.getLogger(__name__)


class ResCompany(models.Model):
    _inherit = 'res.company'

    nitku = fields.Char(string="NITKU", required=True)
    signatory_name = fields.Char(string='Financial Controller')
    signatory_position = fields.Char(string='Financial Controller Position')