<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="report_invoice_receipt">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="o">
                <t t-call="web.basic_layout">
                    <div class="page">
                        <style>
                            .container {
                            border: 2px dashed #ccc;
                            padding: 20px;
                            }
                            .header-content {
                            display: flex;
                            justify-content: space-between;
                            }
                            .company-info {
                            font-weight: bold;
                            }
                            .receipt-title {
                            font-size: 20px;
                            font-weight: bold;
                            text-align: right;
                            }
                            .details {
                            margin-top: 20px;
                            border: 1px solid #ccc;
                            padding: 15px;
                            }
                            .details p {
                            margin: 8px 0;
                            }
                            .label {
                            color: #0072c6;
                            font-weight: bold;
                            }
                            .amount {
                            margin-top: 20px;
                            font-size: 16px;
                            }
                            .signature {
                            display: flex;
                            justify-content: space-between;
                            margin-top: 40px;
                            }
                            .signer {
                            text-align: right;
                            font-size: 14px;
                            }
                            .details {
                            margin-top: 20px;
                            border: 1px solid #ccc;
                            padding: 15px;
                            }
                            .details .table td {
                            padding: 8px 4px;
                            vertical-align: top;
                            }
                            .details .label {
                            color: #0072c6;
                            font-weight: bold;
                            }
                            .amount {
                            margin-top: 20px;
                            }
                            .amount .table td {
                            padding: 8px 4px;
                            vertical-align: top;
                            }
                            .amount .label {
                            color: #0072c6;
                            font-weight: bold;
                            }
                        </style>

                        <div class="container">
                            <!-- Kwitansi Title - Top Right -->
                            <h1>
                                <div class="receipt-title">KWITANSI</div>
                            </h1>

                            <!-- First Row - Company Name -->
                            <h2>
                                <strong>
                                    <span t-field="o.company_id.name"/>
                                </strong>
                            </h2>

                            <!-- Second Row - Company Info and Date/Number -->
                            <table class="table table-borderless" style="width: 100%;">
                                <tr>
                                    <td style="width: 50%;">
                                        <div class="company-info">
                                            <div t-field="o.company_id.street"/>
                                            <div t-field="o.company_id.street2"/>
                                            <div>
                                                <span t-field="o.company_id.city"/>,
                                                <span t-field="o.company_id.state_id"/>
                                                <span t-field="o.company_id.zip"/>
                                            </div>
                                        </div>
                                    </td>
                                    <td style="width: 50%;">
                                        <table class="table table-sm table-borderless float-end">
                                            <tr>
                                                <td class="text-right" style="color: #0072c6;">Date</td>
                                                <td>:</td>
                                                <td>
                                                    <span t-field="o.invoice_date"/>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="text-right" style="color: #0072c6;">Number</td>
                                                <td>:</td>
                                                <td>
                                                    <span t-esc="'KW-LA.' + o.name"/>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>

                            <div class="details">
                                <table class="table table-borderless" style="width: 100%;">
                                    <tr>
                                        <td style="width: 25%;" class="label">Sudah Terima Dari</td>
                                        <td style="width: 5%;">:</td>
                                        <td style="width: 70%;">
                                            <span t-field="o.partner_id.name"/>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="label">Nominal</td>
                                        <td>:</td>
                                        <td>
                                            <!-- <span t-esc="o.currency_id.amount_to_text(o.amount_total)"/> -->
                                            <span t-esc="o.currency_id.with_context(lang=o.partner_id.lang or 'en_US').amount_to_text(o.amount_total)"/>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="label">Untuk Pembayaran</td>
                                        <td>:</td>
                                        <td>
                                            <t t-if="doc_model == 'account.move'">
                                                <span t-esc="o.name"/>
                                            </t>
                                            <t t-else="">
                                                <span t-esc="o.name"/>
                                            </t>
                                        </td>
                                    </tr>
                                </table>
                            </div>

                            <table class="table table-borderless" style="width: 100%; margin-top: 20px;">
                                <tr>
                                    <td style="width: 50%; vertical-align: top;">
                                        <table class="table table-borderless" style="width: 100%;">
                                            <tr>
                                                <td style="width: 25%; color: #0072c6;" class="label">Jumlah</td>
                                                <td style="width: 5%;">:</td>
                                                <td style="width: 70%;">
                                                    <strong t-field="o.amount_total"
                                                            style="text-decoration-line: underline; text-decoration-style: double;"
                                                            t-options='{"widget": "monetary", "display_currency": o.currency_id}'/>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                    <td style="width: 50%; text-align: center;">
                                        Jakarta,
                                        <span t-field="o.invoice_date" t-options='{"format": "d MMMM Y"}'/>
                                        <br/>
                                        <br/>
                                        <br/>
                                        <br/>
                                        <p style="text-decoration: underline;"><t t-esc="o.company_id.signatory_name"/></p>
                                        <span><t t-esc="o.company_id.signatory_position"/></span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </t>
            </t>
        </t>
    </template>

    <record id="action_report_invoice_receipt" model="ir.actions.report">
        <field name="name">Invoice Receipt</field>
        <field name="model">account.move</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">account_sales_bar.report_invoice_receipt</field>
        <field name="report_file">account_sales_bar.report_invoice_receipt</field>
        <field name="binding_model_id" ref="account.model_account_move"/>
        <field name="binding_type">report</field>
    </record>

    <!-- Action for account.move.receivable -->
    <record id="action_report_invoice_receipt_receivable" model="ir.actions.report">
        <field name="name">Invoice Receipt</field>
        <field name="model">account.move.receivable</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">account_sales_bar.report_invoice_receipt</field>
        <field name="report_file">account_sales_bar.report_invoice_receipt</field>
        <field name="binding_model_id" ref="account_sales_bar.model_account_move_receivable"/>
        <field name="binding_type">report</field>
    </record>
</odoo>