# -*- coding: utf-8 -*-
{
    'name': "Bank Statement Reconcile",

    'summary': "Generate Bank Statement Reconcile Wizard",

    'author': "Akuntplus"
              "Ario Dwiponco",
    'website': "http://www.akuntplus.id",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/15.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    'category': 'Accounting',
    'version': '1.0',

    # any module necessary for this one to work correctly
    'depends': ['base', 'mail', 'account_accountant', 'account_statement_base','account_inherit','ap_mt940_config', 'applied_invoice','account_payment_plan'],

    # always loaded
    'data': [
        'data/ir_sequence.xml',
        'data/ir_cron_data.xml',
        'security/ir.model.access.csv',
        # 'views/account_bank_statement_views.xml',
        'views/bank_statement_views.xml',
        'views/reconcile_matching_rule_view.xml',
        'wizards/bank_balancing_wizard_views.xml',
        'wizards/bank_statement_search_wizard_views.xml',
        'views/menu_items.xml',
        'views/res_config_settings_views.xml',
        'views/sftp_config_views.xml',
    ],

}
