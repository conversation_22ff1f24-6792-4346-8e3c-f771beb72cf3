from odoo import models, fields, api
from odoo.exceptions import ValidationError, UserError
from psycopg2.extras import execute_values
import logging

_logger = logging.getLogger(__name__)




class BankStatementSearchWizard(models.TransientModel):
    _name = 'bank.statement.search.wizard'
    _description = 'Bank Statement Search Wizard'

    journal_id = fields.Many2one('account.journal', string='Bank Account')
    period_start = fields.Date(string='Period Start')
    period_end = fields.Date(string='Period End')
    statement_id = fields.Char(string='Statement ID')
    bank_statement_id = fields.Many2one('bank.statement', string='Bank Statement', default=lambda self: self.env.context.get('default_bank_statement_id'))
    reconcile_rule_id = fields.Many2one('reconcile.matching.rule', string='Reconcile Rule', default=lambda self: self.env.context.get('default_reconcile_rule_id'))
    sys_date_from = fields.Date(string='System Transaction Date From', required=True)
    sys_date_to = fields.Date(string='System Transaction Date To', required=True)
    selected_statement_count = fields.Integer('Selected', compute='_compute_selected_statement_counts')
    selected_move_line_count = fields.Integer('Selected', compute='_compute_selected_move_counts')
    statement_line_amount = fields.Monetary('Amount', compute='_compute_selected_statement_counts')
    move_line_amount = fields.Monetary('Amount', compute='_compute_selected_move_counts')
    company_id = fields.Many2one(string="Company", comodel_name="res.company", related="journal_id.company_id")
    currency_id = fields.Many2one(string="Journal Currency", comodel_name="res.currency", compute="_compute_currency_id")
    selected_move_currency_id = fields.Many2one(string="Currency", comodel_name="res.currency")
    difference = fields.Monetary('Difference', compute="compute_difference")

    # statement_line_ids = fields.One2many('search.wizard.statement.line', 'wizard_id', string='Bank Statement Lines')
    statement_line_ids = fields.One2many(related='bank_statement_id.incomplete_line_ids', string='Bank Statement Lines', readonly=False)
    move_line_ids = fields.One2many('search.wizard.move.line', 'wizard_id', string='System Transactions')

    is_reconcilable = fields.Boolean('is_reconcilable', compute="compute_is_reconcilable")
    need_adjustment = fields.Boolean('need_adjustment')

    step = fields.Selection(
        [("step1", "Step 1"), ("step2", "Step 2"), ("step3", "Step 3")], default="step1"
    )
    adjustment_date = fields.Date(string='Date', default=fields.Date.context_today, required=True)
    adjustment_reference = fields.Char(string='Reference')
    adjustment_description = fields.Char(string='Description')

    @api.depends("journal_id")
    def _compute_currency_id(self):
        for rec in self:
            rec.currency_id = rec.journal_id.currency_id or rec.company_id.currency_id

    def _prepare_statement_line_vals(self, statement_lines):
        statement_line_vals = []
        for line in statement_lines:
            reference = f"{line.line_number} - {line.statement_id.statement_id or ''}"
            statement_line_vals.append((0, 0, {
                'is_selected': False,
                'statement_line_id': line.id,
                'reference': reference,
                'description': line.description,
                'transaction_date': line.transaction_date,
                'amount': -(line.debit) if line.debit else line.credit,
            }))
        return statement_line_vals
    
    def search_move_lines(self):
        sql = """ 
        select
            aml.id,
            aml."name",
            aj.id as journal_id,
            coalesce(aj."name" ->> 'id_ID', aj."name" ->> 'en_US') as journal_name,
            aa.code_store ->> '1' as account_code,
            coalesce(aa."name" ->> 'id_ID', aa."name" ->> 'en_US') as account_name,
            aml."date",
            aa.account_type,
            rc.id as currency_id,
            rc."name" as currency_name,
            (select rc.currency_id from res_company rc limit 1) as company_currency_id,
            aml.debit,
            aml.credit,
            case
                when aml.debit > 0 then aml.debit
            else
                -aml.credit
            end as amount_due,
            aml.amount_currency,
            aml.deposit_settlement,
            aml.price_total,
            aml.reconciled
        from account_move_line aml
        inner join account_account aa on
            aml.account_id = aa.id
        inner join account_move am on
            am.id = aml.move_id
        inner join account_journal aj on
            aj.id = am.journal_id
        inner join res_currency rc on
            aml.currency_id = rc.id
        where
            -- ambil date journal entry karena deposit settlemtn banyak yang kosong datenya.
            -- harusnya date journal items.
            (am."date" between %s and %s)
            and am.state not in ('draft', 'reversed', 'cancelled')
            and aa.account_type not in ('asset_receivable', 'liability_payable')
            and (aml.reconciled = false or aml.reconciled is null)
        """
        params = [self.sys_date_from, self.sys_date_to]

        # Filter jurnal currency
        if self.journal_id.currency_id:
            sql += " and rc.id = %s "
            params.append(self.journal_id.currency_id.id)

        company_id = self.env.user.company_id
        deposit_id = company_id.journal_deposit_id.id
        settlement_id = company_id.journal_settlement_id.id
        if not self.reconcile_rule_id: # No recon rule: show all reconcilable journal
            sql += " and (aa.reconcile = true "
        else:
            if self.reconcile_rule_id.transaction_mode:
                reconcile_journal_ids = self.reconcile_rule_id.journal_transaction_ids.ids
                if reconcile_journal_ids: # Transaction mode and has journals

                    ids_set = set(reconcile_journal_ids)
                    has_deposit = deposit_id in ids_set
                    has_settlement = settlement_id in ids_set

                    if ids_set == {deposit_id, settlement_id}: # both deposit and settlement
                        sql += " and ((aj.id = %s and aa.account_type = 'asset_cash') or (aj.id = %s and aa.account_type = 'asset_cash')"
                        params.append(deposit_id)
                        params.append(settlement_id)
                    elif ids_set == {settlement_id}: # only settlement
                        sql += " and ((aj.id = %s and aa.account_type = 'asset_cash') "
                        params.append(settlement_id)
                    elif ids_set == {deposit_id}: # only deposit
                        sql += " and ((aj.id = %s and aa.account_type = 'asset_cash') "
                        params.append(deposit_id)
                    else:
                        # Filter deposit/settlement journal di matching rule
                        # karena deposit/settlement journal boleh tidak allowed reconcile
                        reconcile_journal_ids[:] = [
                            id for id in reconcile_journal_ids
                            if id not in {deposit_id, settlement_id}
                        ]

                        sql += " and ((aj.id IN %s and aa.reconcile = true) "
                        params.append(tuple(reconcile_journal_ids))

                        if has_deposit:
                            sql += " or (aj.id = %s and aa.account_type = 'asset_cash') "
                            params.append(deposit_id)
                        if has_settlement:

                            sql += " or (aj.id = %s and aa.account_type = 'asset_cash') "
                            params.append(settlement_id)
                else:  # Transaction mode but empty: show no journal
                    sql += " and (aj.id is null "
            else:
                sql += " and (aa.reconcile = true " # Show all reconcilable journal
                if deposit_id:
                    sql += " or (aj.id = %s and aa.account_type = 'asset_cash') "
                    params.append(deposit_id)
                if settlement_id:
                    sql += " or (aj.id = %s and aa.account_type = 'asset_cash') "
                    params.append(settlement_id)

        sql += """) order by am."date" desc"""

        print(sql)
        print(params)

        self.env.cr.execute(sql, tuple(params))
        return self.env.cr.dictfetchall() #list of dicts

    def search_transaction(self):
        self.move_line_ids.unlink()

        move_lines = self.search_move_lines()

        move_line_vals = []
        for line in move_lines:
            # move_line_vals.append((0, 0, {
            #     'is_selected': False,
            #     'move_line_id': line["id"],
            #     'amount_due': line["amount_due"],
            #     'amount_due_currency': line["amount_currency"] # amount pada saat transaksi di currency
            # }))
            move_line_vals.append((self.id, False, line['id'], line['amount_due'], line['amount_currency']))

        # self.move_line_ids = move_line_vals
        execute_values(self.env.cr, """
            INSERT INTO search_wizard_move_line (wizard_id, is_selected, move_line_id, amount_due, amount_due_currency) VALUES %s 
        """, move_line_vals)

        return {
            'name': 'Search Bank Statements',
            'type': 'ir.actions.act_window',
            'res_model': self._name,
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
            # 'target': 'fullscreen'
    }
    
    @api.depends('statement_line_ids.is_selected')
    def _compute_selected_statement_counts(self):
        for rec in self:
            selected_lines = rec.statement_line_ids.filtered(lambda x: x.is_selected)
            rec.selected_statement_count = len(selected_lines) if selected_lines else 0

            # Handle if more than one selected
            # if selected_lines and len(selected_lines) > 1:
            #     selected_line = self.env['bank.statement.line'].search([
            #         ('is_selected', '=', True),
            #         ('statement_id', '=', self.bank_statement_id.id)
            #     ], limit=1)
            #
            #     selected_statement_id = selected_line.id if selected_line else None
            #
            #     if selected_statement_id:
            #         # Unselect all except selected_statement_id
            #         for line in selected_lines:
            #             if line.ids[0] == selected_statement_id:
            #                 line.is_selected = False
            #                 selected_lines = selected_lines.filtered(lambda l: l.ids[0] != selected_statement_id)

            if selected_lines:
                params = (self.bank_statement_id.id, tuple(selected_lines.ids),)
                # sql = "UPDATE bank_statement_line SET is_selected = FALSE WHERE statement_id = %s AND id NOT IN %s"
                # self.env.cr.execute(sql, params)

                sql = "UPDATE bank_statement_line SET is_selected = TRUE WHERE statement_id = %s AND id IN %s"
                self.env.cr.execute(sql, params)
                rec.selected_move_currency_id = selected_lines[0].currency_id
            else:
                bank_statement_id = self.env.context.get('default_bank_statement_id')
                sql = "UPDATE bank_statement_line SET is_selected = FALSE WHERE statement_id = %s"
                self.env.cr.execute(sql, (bank_statement_id,))
                rec.selected_move_currency_id = None
            
            rec.statement_line_amount = sum(line.amount for line in selected_lines)

    @api.depends('move_line_ids.is_selected')
    def _compute_selected_move_counts(self):
        for rec in self:
            selected_lines = rec.move_line_ids.filtered(lambda x: x.is_selected)
            rec.selected_move_line_count = len(selected_lines)
            rec.move_line_amount = sum(line.amount_due_currency for line in selected_lines)

    @api.depends('statement_line_amount','move_line_amount')
    def compute_difference(self):
        for rec in self:
            selected_statement_lines = rec.statement_line_ids.filtered(lambda x: x.is_selected)
            selected_move_lines = rec.move_line_ids.filtered(lambda x: x.is_selected)

            # no bank lines selected
            if not selected_statement_lines:
                pass
            else:
                selected_statement_line = selected_statement_lines[0]
                statement_currency_id = selected_statement_line.currency_id.id
                if not all(line.currency_id.id == statement_currency_id for line in selected_move_lines):
                    raise ValidationError("Selected system transaction must have the same currency as the selected bank statement line.")

            rec.difference = rec.statement_line_amount - rec.move_line_amount
   
    @api.depends('selected_move_line_count', 'selected_statement_count')
    def compute_is_reconcilable(self):
        for rec in self:
            rec.is_reconcilable = False
            rec.need_adjustment = False

            tolerance_in_idr = float(self.env['ir.config_parameter'].sudo().get_param('account_statement_reconcile.general_amount_tolerance'))
            move_lines = self.move_line_ids.filtered(lambda x: x.is_selected)
            move_line = move_lines[0] if move_lines else None
            if move_line:
                rate = 1 / move_line.move_line_id.currency_rate
                tolerance_in_idr = tolerance_in_idr / rate if rate > 1 else tolerance_in_idr

            if rec.selected_move_line_count > 0 and rec.selected_statement_count > 0:
                diff = abs(rec.difference)
                # if diff == 0 or diff <= float(tolerance_in_idr):
                rec.is_reconcilable = True
                # else:
                #     rec.is_reconcilable = False
                # elif diff <= float(tolerance):
                #     rec.is_reconcilable = True
                #     rec.need_adjustment = True

    def manual_reconcile(self):
        try:
            selected_statement_lines = self.statement_line_ids.filtered(lambda x: x.is_selected)
            selected_move_lines = self.move_line_ids.filtered(lambda x: x.is_selected)
            move_line_ids = selected_move_lines.mapped('move_line_id.id')

            if self.validate_only_deposit_or_settlement(selected_move_lines):
                # Only deposit and settlement reconcile
                self.create_matching_number(move_line_ids)

                move_ids = selected_move_lines.mapped('move_id')
                reconcile_refs = ''
                if len(move_ids) == 1:
                    reconcile_refs = move_ids.name  # hanya satu move
                else:
                    reconcile_refs = ', '.join(set(selected_move_lines.mapped('move_id.name')))

                for line in selected_statement_lines:
                    line.state = 'completed'
                    line.reconcile_ref = reconcile_refs

            else:
                #Only non deposit and settlement reconcile
                move_ids = self.create_account_move_new(selected_statement_lines, selected_move_lines)
                for move in move_ids:
                    move_id = self.env['account.move'].browse(move[0])
                    move_id.write({'state': 'draft'})
                    move_id.action_post()
                    move_id.ref = move_id.name
                    self.create_matching_number(move_id.line_ids.ids)
                    # move_id = self.create_journal_entry()

                    for line in selected_statement_lines:
                        if line.id == move[1]:
                            line.state = 'completed'
                            line.move_id = move_id

            for line in selected_move_lines:
                line.move_line_id.reconciled = True

            bank_statement = self.env['bank.statement'].browse(self.bank_statement_id.id)
            if not bank_statement.incomplete_line_ids:
                bank_statement.state = 'completed'
            else:
                bank_statement.state = 'incomplete'

            self.clear_bank_statement_selections()

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                # 'tag': 'reload',
                'params': {
                    'title': 'Bank Statement Reconciliation',
                    'message': 'Bank statement reconciled successfully!',
                    'type': 'success',
                    'sticky': False,
                    'next': {'type': 'ir.actions.act_window_close'},
                }
            }
        except Exception as e:
            raise UserError(f"Error reconciling statement: {str(e)}")

    def create_account_move(self, statement_lines, move_lines):
        cr = self.env.cr
        uid = self.env.uid

        # Get reconcile journal
        reconcile_journal_id = self.env.user.company_id.reconcile_journal_id
        if not reconcile_journal_id:
            raise ValueError("Reconcile journal is not set")

        debit_journal_id = self.bank_statement_id.journal_id
        if not debit_journal_id:
            raise ValueError("Statement journal is not set")

        # Get account for debit line
        debit_account_id = self.bank_statement_id.journal_id.default_account_id
        if not debit_account_id:
            raise ValueError("Debit account is not set")

        # Get company's currency (IDR)
        company_id = self.env.user.company_id
        company_currency_id = company_id.currency_id

        # Get move line
        move_line = move_lines[0] if move_lines else None
        if not move_line:
            raise ValueError("No system transaction selected")

        statement_line = statement_lines[0] if statement_lines else None
        if not statement_line:
            raise ValueError("No bank statement selected")

        # foreign currency (IDR/USD/...etc)
        foreign_currency_id = move_line.currency_id
        if not foreign_currency_id:
            raise ValueError("System transaction currency is not set")

        debit_amount = 0 # USD 890 or IDR ********
        rate = 0

        total_debit_foreign = 0
        total_debit_idr = 0

        for s_line in statement_lines:
            debit_amount = s_line.amount  # USD 890 or IDR ********
            rate = 1 / move_line.move_line_id.currency_rate
            total_debit_foreign += debit_amount
            total_debit_idr += total_debit_foreign * rate if company_currency_id.id != foreign_currency_id.id else debit_amount

        # ---- Get period id and fiscal year ---- #
        cr.execute("""
          SELECT p.id, fiscal_year_id FROM sh_account_period p WHERE p.date_start <= now()
                   AND p.date_end >= now();
        """)
        period_id = cr.dictfetchone()

        # ---- Insert account move ---- #
        move_name = "/"
        move_date = fields.Date.today()

        cr.execute("""
            INSERT INTO account_move (
                name, date, journal_id, move_type, state, auto_post, period_id, invoice_date_due,
                company_id, currency_id, create_uid, create_date, write_uid, write_date,
                payment_state, ref
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, now(),
                %s, %s, %s, now(), %s, now(),
                %s, %s
            ) RETURNING id
        """, (
            move_name, move_date, reconcile_journal_id.id, 'entry', 'draft', 'no', period_id.get("id"),
            company_id.id, company_currency_id.id, uid, uid,
            'paid', move_name
        ))
        move_id = cr.fetchone()[0]

        # ---- Insert debit line ---- #
        cr.execute("""
            INSERT INTO account_move_line (
                reconciled, move_id, account_id, name, debit, credit,
                balance, amount_currency, currency_id, display_type,
                company_id, date, create_uid, create_date, write_uid, write_date,
                journal_id, company_currency_id, sequence, move_name, parent_state, ref,
                amount_residual, amount_residual_currency, quantity, price_unit, price_total,
                period_id, fiscal_year, currency_conversion_id
            ) VALUES (
                %s, %s, %s, %s, %s, %s,
                %s, %s, %s, %s,
                (SELECT company_id FROM account_journal WHERE id = %s), %s, %s, now(), %s, now(),
                %s, %s, %s, %s, %s, %s,
                0, 0, 1, %s, %s,
                %s, %s, %s
            )
        """, (
            True, move_id, debit_account_id.id, statement_line.description if statement_line.description else "", total_debit_idr, 0.0,
            total_debit_idr, total_debit_foreign, foreign_currency_id.id, 'product',
            debit_journal_id.id, move_date, uid, uid,
            reconcile_journal_id.id, company_currency_id.id, 10, move_name, 'draft', move_name,
            total_debit_foreign, total_debit_foreign,
            period_id.get("id"), period_id.get("fiscal_year_id"), foreign_currency_id.id
        ))

        # ---- Insert credit line ---- #
        sum_total_credit_foreign = 0

        for credit_line in move_lines:
            credit_amount = credit_line.amount_due_currency
            total_credit_foreign = credit_amount
            total_credit_idr = total_credit_foreign * rate if company_currency_id.id != foreign_currency_id.id else credit_amount

            # credit_journal_id = move_line.move_line_id.journal_id
            # if not credit_journal_id:
            #     raise ValueError(f"Credit journal ID is NULL found for journal item id: {move_line.move_line_id.id} ")

            # journal id dibaca ke account_move karena di account_move_line = 0
            credit_journal_id = move_line.move_id.journal_id

            sum_total_credit_foreign = sum_total_credit_foreign + total_credit_foreign

            cr.execute("""
                INSERT INTO account_move_line (
                    reconciled, move_id, account_id, name, debit, credit,
                    balance, amount_currency, currency_id, display_type,
                    company_id, date, create_uid, create_date, write_uid, write_date,
                    journal_id, company_currency_id, sequence, move_name, parent_state, ref,
                    amount_residual, amount_residual_currency, quantity, price_unit, price_total,
                    period_id, fiscal_year, currency_conversion_id
                ) VALUES (
                    %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    (SELECT company_id FROM account_journal WHERE id = %s), %s, %s, now(), %s, now(),
                    %s, %s, %s, %s, %s, %s,
                    0, 0, 1, %s, %s,
                    %s, %s, %s
                )
            """, (
                True, move_id, move_line.account_id.id, move_line.name if move_line.name else "", 0.0, total_credit_idr,
                -total_credit_idr, -total_credit_foreign, foreign_currency_id.id, 'product',
                credit_journal_id.id, move_date, uid, uid,
                reconcile_journal_id.id, company_currency_id.id, 10, move_name, 'draft', move_name,
                total_credit_foreign, total_credit_foreign,
                period_id.get("id"), period_id.get("fiscal_year_id"), foreign_currency_id.id
            ))

        total_difference_foreign = 0
        if(self.difference):
            difference_account_id = float(self.env['ir.config_parameter'].sudo().get_param('account_statement_reconcile.difference_account'))
            if not difference_account_id:
                raise ValidationError("Difference/Rounding Account is not set")

            difference_amount = self.difference
            total_difference_foreign = difference_amount
            total_difference_idr = total_difference_foreign * rate if company_currency_id.id != foreign_currency_id.id else difference_amount

            debit = credit = 0
            if(total_difference_foreign > 0):
                credit = total_difference_foreign
            else:
                debit = abs(total_difference_foreign)

            cr.execute("""
                INSERT INTO account_move_line (
                    reconciled, move_id, account_id, name, debit, credit,
                    balance, amount_currency, currency_id, display_type,
                    company_id, date, create_uid, create_date, write_uid, write_date,
                    journal_id, company_currency_id, sequence, move_name, parent_state, ref,
                    amount_residual, amount_residual_currency, quantity, price_unit, price_total,
                    period_id, fiscal_year, currency_conversion_id
                ) VALUES (
                    %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    (SELECT company_id FROM account_journal WHERE id = %s), %s, %s, now(), %s, now(),
                    %s, %s, %s, %s, %s, %s,
                    0, 0, 1, %s, %s,
                    %s, %s, %s
                )
            """, (
                True, move_id, difference_account_id, "Rounding", debit, credit,
                -total_difference_idr, -total_difference_foreign, foreign_currency_id.id, 'product',
                reconcile_journal_id.id, move_date, uid, uid,
                reconcile_journal_id.id, company_currency_id.id, 10, move_name, 'draft', move_name,
                total_difference_foreign, total_difference_foreign,
                period_id.get("id"), period_id.get("fiscal_year_id"), foreign_currency_id.id
            ))


        sum_total_credit_foreign = sum_total_credit_foreign + total_difference_foreign
        # print(f"total_debit_foreign: {total_debit_foreign}")
        # print(f"sum_total_credit_foreign: {sum_total_credit_foreign}")

        if total_debit_foreign != sum_total_credit_foreign:
            raise ValueError(f"Total debit: {total_debit_foreign} and total credit: {sum_total_credit_foreign} is not balanced")

        return move_id

    def create_account_move_new(self, statement_lines, move_lines):
        cr = self.env.cr
        uid = self.env.uid

        # Get reconcile journal
        reconcile_journal_id = self.env.user.company_id.reconcile_journal_id
        if not reconcile_journal_id:
            raise ValueError("Reconcile journal is not set")

        debit_journal_id = self.bank_statement_id.journal_id
        if not debit_journal_id:
            raise ValueError("Statement journal is not set")

        # Get account for debit line
        debit_account_id = self.bank_statement_id.journal_id.default_account_id
        if not debit_account_id:
            raise ValueError("Debit account is not set")

        # Get company's currency (IDR)
        company_id = self.env.user.company_id
        company_currency_id = company_id.currency_id

        # Get move line
        move_line = move_lines[0] if move_lines else None
        if not move_line:
            raise ValueError("No system transaction selected")

        statement_line = statement_lines[0] if statement_lines else None
        if not statement_line:
            raise ValueError("No bank statement selected")

        # foreign currency (IDR/USD/...etc)
        foreign_currency_id = move_line.currency_id
        if not foreign_currency_id:
            raise ValueError("System transaction currency is not set")

        selected_lines = [s_line for s_line in statement_lines]
        selected_move_lines = [move_line.copy() for move_line in move_lines]

        if self.difference:
            difference_account_id = float(self.env['ir.config_parameter'].sudo().get_param('account_statement_reconcile.difference_account'))
            if not difference_account_id:
                raise ValidationError("Difference/Rounding Account is not set")

        # ---- Get period id and fiscal year ---- #
        cr.execute("""
              SELECT p.id, fiscal_year_id FROM sh_account_period p WHERE p.date_start <= date(now())
                   AND p.date_end >= date(now());
        """)
        period_id = cr.dictfetchone()

        move_lines_index = 0
        move_ids = []
        list_reconcile = []
        done_reconcile = False

        for line in selected_lines:
            # ---- Insert account move ---- #
            move_name = "/"
            move_date = fields.Date.today()

            cr.execute("""
                INSERT INTO account_move (
                    name, date, journal_id, move_type, state, auto_post, period_id, invoice_date_due,
                    company_id, currency_id, create_uid, create_date, write_uid, write_date,
                    payment_state, ref
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, now(),
                    %s, %s, %s, now(), %s, now(),
                    %s, %s
                ) RETURNING id
            """, (
                move_name, move_date, reconcile_journal_id.id, 'entry', 'posted', 'no', period_id.get("id"),
                company_id.id, company_currency_id.id, uid, uid,
                'paid', move_name
            ))
            move_id = cr.fetchone()[0]
            move_ids.append([move_id, line.id])

            debit_amount = line.amount  # USD 890 or IDR ********
            rate = 1 / move_line.move_line_id.currency_rate
            total_debit_foreign = debit_amount
            total_debit_idr = total_debit_foreign * rate if company_currency_id.id != foreign_currency_id.id else debit_amount

            # ---- Insert debit line ---- #
            cr.execute("""
                INSERT INTO account_move_line (
                    reconciled, move_id, account_id, name, debit, credit,
                    balance, amount_currency, currency_id, display_type,
                    company_id, date, create_uid, create_date, write_uid, write_date,
                    journal_id, company_currency_id, sequence, move_name, parent_state, ref,
                    amount_residual, amount_residual_currency, quantity, price_unit, price_total,
                    period_id, fiscal_year, currency_conversion_id
                ) VALUES (
                    %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    (SELECT company_id FROM account_journal WHERE id = %s), %s, %s, now(), %s, now(),
                    %s, %s, %s, %s, %s, %s,
                    0, 0, 1, %s, %s,
                    %s, %s, %s
                )
            """, (
                False, move_id, debit_account_id.id, statement_line.description if statement_line.description else "", total_debit_idr, 0.0,
                total_debit_idr, total_debit_foreign, foreign_currency_id.id, 'product',
                debit_journal_id.id, move_date, uid, uid,
                reconcile_journal_id.id, company_currency_id.id, 10, move_name, 'draft', move_name,
                total_debit_foreign, total_debit_foreign,
                period_id.get("id"), period_id.get("fiscal_year_id"), foreign_currency_id.id
            ))

            remaining = debit_amount
            sum_total_credit_foreign = 0
            # Ambil dari transaction sampai cukup
            reconcile_account = False
            if debit_amount > 0:
                while remaining > 0 and move_lines_index < len(selected_move_lines):
                    txn = selected_move_lines[move_lines_index]
                    list_reconcile.append(selected_move_lines[move_lines_index].move_line_id.id)
                    if txn.amount_due_currency <= remaining:
                        # Ambil seluruh amount transaction
                        credit_amount = txn.amount_due_currency
                        remaining -= txn.amount_due_currency
                        move_lines_index += 1
                    else:
                        # Ambil sebagian dari transaction
                        credit_amount = remaining
                        txn.amount_due_currency -= remaining
                        remaining = 0
                    total_credit_foreign = credit_amount
                    total_credit_idr = total_credit_foreign * rate if company_currency_id.id != foreign_currency_id.id else credit_amount

                    credit_journal_id = txn.move_id.journal_id

                    sum_total_credit_foreign = sum_total_credit_foreign + total_credit_foreign

                    cr.execute("""
                        INSERT INTO account_move_line (
                            reconciled, move_id, account_id, name, debit, credit,
                            balance, amount_currency, currency_id, display_type,
                            company_id, date, create_uid, create_date, write_uid, write_date,
                            journal_id, company_currency_id, sequence, move_name, parent_state, ref,
                            amount_residual, amount_residual_currency, quantity, price_unit, price_total,
                            period_id, fiscal_year, currency_conversion_id
                        ) VALUES (
                            %s, %s, %s, %s, %s, %s,
                            %s, %s, %s, %s,
                            (SELECT company_id FROM account_journal WHERE id = %s), %s, %s, now(), %s, now(),
                            %s, %s, %s, %s, %s, %s,
                            0, 0, 1, %s, %s,
                            %s, %s, %s
                        ) RETURNING id
                    """, (
                        False, move_id, txn.account_id.id, txn.name if txn.name else "", 0.0, total_credit_idr,
                        -total_credit_idr, -total_credit_foreign, foreign_currency_id.id, 'product',
                        credit_journal_id.id, move_date, uid, uid,
                        reconcile_journal_id.id, company_currency_id.id, 10, move_name, 'posted', move_name,
                        total_credit_foreign, total_credit_foreign,
                        period_id.get("id"), period_id.get("fiscal_year_id"), foreign_currency_id.id
                    ))
                    reconcile_account = txn.account_id.id
                    move_line_id = cr.fetchone()[0]
                    search_move_line = self.env['account.move.line'].browse(move_line_id)
                    if remaining == 0:
                        if search_move_line:
                            search_move_line._compute_amount_residual()
                    list_reconcile.append(move_line_id)
            else:
                while remaining < 0 and move_lines_index < len(selected_move_lines):
                    txn = selected_move_lines[move_lines_index]
                    list_reconcile.append(selected_move_lines[move_lines_index].move_line_id.id)
                    if txn.amount_due_currency >= remaining:
                        # Ambil seluruh amount transaction
                        credit_amount = txn.amount_due_currency
                        remaining -= txn.amount_due_currency
                        move_lines_index += 1
                    else:
                        # Ambil sebagian dari transaction
                        credit_amount = remaining
                        txn.amount_due_currency -= remaining
                        remaining = 0
                    total_credit_foreign = credit_amount
                    total_credit_idr = total_credit_foreign * rate if company_currency_id.id != foreign_currency_id.id else credit_amount

                    credit_journal_id = txn.move_id.journal_id

                    sum_total_credit_foreign = sum_total_credit_foreign + total_credit_foreign

                    cr.execute("""
                        INSERT INTO account_move_line (
                            reconciled, move_id, account_id, name, debit, credit,
                            balance, amount_currency, currency_id, display_type,
                            company_id, date, create_uid, create_date, write_uid, write_date,
                            journal_id, company_currency_id, sequence, move_name, parent_state, ref,
                            amount_residual, amount_residual_currency, quantity, price_unit, price_total,
                            period_id, fiscal_year, currency_conversion_id
                        ) VALUES (
                            %s, %s, %s, %s, %s, %s,
                            %s, %s, %s, %s,
                            (SELECT company_id FROM account_journal WHERE id = %s), %s, %s, now(), %s, now(),
                            %s, %s, %s, %s, %s, %s,
                            0, 0, 1, %s, %s,
                            %s, %s, %s
                        ) RETURNING id
                    """, (
                        False, move_id, txn.account_id.id, txn.name if txn.name else "", 0.0, total_credit_idr,
                        -total_credit_idr, -total_credit_foreign, foreign_currency_id.id, 'product',
                        credit_journal_id.id, move_date, uid, uid,
                        reconcile_journal_id.id, company_currency_id.id, 10, move_name, 'posted', move_name,
                        total_credit_foreign, total_credit_foreign,
                        period_id.get("id"), period_id.get("fiscal_year_id"), foreign_currency_id.id
                    ))
                    reconcile_account = txn.account_id.id
                    move_line_id = cr.fetchone()[0]
                    search_move_line = self.env['account.move.line'].browse(move_line_id)
                    if remaining == 0:
                        if search_move_line:
                            search_move_line._compute_amount_residual()
                    list_reconcile.append(move_line_id)

            # Jika total transaction lebih kecil dari statement line
            if remaining != 0:
                difference_amount = remaining
                total_difference_foreign = difference_amount
                total_difference_idr = total_difference_foreign * rate if company_currency_id.id != foreign_currency_id.id else difference_amount

                debit = credit = 0
                if(total_difference_foreign > 0):
                    credit = total_difference_foreign
                else:
                    debit = abs(total_difference_foreign)

                cr.execute("""
                    INSERT INTO account_move_line (
                        reconciled, move_id, account_id, name, debit, credit,
                        balance, amount_currency, currency_id, display_type,
                        company_id, date, create_uid, create_date, write_uid, write_date,
                        journal_id, company_currency_id, sequence, move_name, parent_state, ref,
                        amount_residual, amount_residual_currency, quantity, price_unit, price_total,
                        period_id, fiscal_year, currency_conversion_id
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s,
                        %s, %s, %s, %s,
                        (SELECT company_id FROM account_journal WHERE id = %s), %s, %s, now(), %s, now(),
                        %s, %s, %s, %s, %s, %s,
                        0, 0, 1, %s, %s,
                        %s, %s, %s
                    ) RETURNING id
                """, (
                    False, move_id, difference_account_id, "Rounding", debit, credit,
                    -total_difference_idr, -total_difference_foreign, foreign_currency_id.id, 'product',
                    reconcile_journal_id.id, move_date, uid, uid,
                    reconcile_journal_id.id, company_currency_id.id, 10, move_name, 'posted', move_name,
                    total_difference_foreign, total_difference_foreign,
                    period_id.get("id"), period_id.get("fiscal_year_id"), foreign_currency_id.id
                ))
                move_line_id = cr.fetchone()[0]
                move_line = self.env['account.move.line'].browse(move_line_id)
                if remaining < 0:
                    list_reconcile.append(move_line_id)
                sum_total_credit_foreign = sum_total_credit_foreign + total_difference_foreign
                to_reconcile = self.env['account.move.line'].search([('id', 'in', list_reconcile)])
                old_account = [x.account_id.id for x in to_reconcile]
                curr_account = to_reconcile[0].account_id.id
                to_reconcile.write({'account_id': curr_account})
                if to_reconcile:
                    to_reconcile._compute_amount_residual()
                if len(to_reconcile) > 1:
                    try:
                        to_reconcile.reconcile()
                        _logger.info("Successfully reconciled move lines: %s", to_reconcile.ids)
                    except Exception as e:
                        _logger.error("Error during reconciliation: %s", str(e))
                done_reconcile = True
                for i in range(0, len(to_reconcile)):
                    cr.execute("""
                        UPDATE account_move_line set account_id = %s WHERE id = %s
                    """, (int(old_account[i]), to_reconcile[i].id))
                for matching_line in to_reconcile:
                    if matching_line.payment_id:
                        for payment_invoice in matching_line.payment_id.payment_invoice_ids:
                            payment_invoice.move_id._compute_payment_state()
                    applied_invoice = self.env['miscellaneous.miscellaneous'].search([('move_id', '=', matching_line.move_id.id)])
                    for applied in applied_invoice:
                        for invoice in applied.invoice_ids:
                            invoice.invoice_id._compute_payment_state()

            if total_debit_foreign != sum_total_credit_foreign:
                raise ValueError(f"Total debit: {total_debit_foreign} and total credit: {sum_total_credit_foreign} is not balanced")
        if not done_reconcile:
            to_reconcile = self.env['account.move.line'].search([('id', 'in', list_reconcile)])
            if len(to_reconcile) > 1:
                try:
                    to_reconcile.reconcile()
                    _logger.info("Successfully reconciled move lines: %s", to_reconcile.ids)
                except Exception as e:
                    _logger.error("Error during reconciliation: %s", str(e))
            for matching_line in to_reconcile:
                if matching_line.payment_id:
                    for payment_invoice in matching_line.payment_id.payment_invoice_ids:
                        payment_invoice.move_id._compute_payment_state()
                applied_invoice = self.env['miscellaneous.miscellaneous'].search([('move_id', '=', matching_line.move_id.id)])
                for applied in applied_invoice:
                    for invoice in applied.invoice_ids:
                        invoice.invoice_id._compute_payment_state()

        return move_ids

    def create_matching_number(self, line_ids):
        # lines_to_reconcile = move.line_ids
        # line_ids = lines_to_reconcile.ids
        
        # Buat full reconcile baru
        self.env.cr.execute("""
            INSERT INTO account_full_reconcile (create_uid, create_date, write_uid, write_date)
            VALUES (%s, NOW(), %s, NOW()) RETURNING id
        """, (self.env.uid, self.env.uid))

        full_reconcile_id = self.env.cr.fetchone()[0]

        # Update matching number tiap jurnal item
        self.env.cr.execute("""
            UPDATE account_move_line
            SET full_reconcile_id = %s, matching_number = %s, reconciled = true
            WHERE id = ANY(%s)
        """, (full_reconcile_id, full_reconcile_id, line_ids))

    # If all lines all deposit or settlement, then yes.
    # If mixed then raise error
    def validate_only_deposit_or_settlement(self, move_lines):
        company_id = self.env.user.company_id
        deposit_settlement_ids = {company_id.journal_deposit_id.id, company_id.journal_settlement_id.id}

        other_ids = {line.move_id.journal_id.id for line in move_lines}

        # Tidak ada deposit/settlement sama sekali: return False
        if not other_ids & deposit_settlement_ids:
            return False

        if other_ids & deposit_settlement_ids and other_ids - deposit_settlement_ids:
            raise UserError(
                "Cannot mix deposit/settlement journals with other journals in reconciliation."
            )
        return True

    def create_journal_entry(self):
        journal_id = self.env.user.company_id.reconcile_journal_id
        if not journal_id:
            raise ValueError("Reconcile journal is not set")

        next_sequence = self.env['ir.sequence'].next_by_code('bank_statement_reconcile_sequence') or 'New'
        move_lines = self._prepare_move_line(next_sequence)

        move_vals = {
            'name': next_sequence,
            'ref': next_sequence,
            'journal_id': journal_id.id,
            'date': fields.Date.today(),
            'move_type': 'entry',
            'payment_state': 'paid',
            'line_ids': move_lines,
        }

        move = self.env['account.move'].with_context(skip_invoice_sync=True, check_move_validity=False).create(move_vals)
        move.action_post()

        lines_to_reconcile = move.line_ids
        line_ids = lines_to_reconcile.ids
        
        # Buat full reconcile baru
        self.env.cr.execute("""
            INSERT INTO account_full_reconcile (create_uid, create_date, write_uid, write_date)
            VALUES (%s, NOW(), %s, NOW()) RETURNING id
        """, (self.env.uid, self.env.uid))

        full_reconcile_id = self.env.cr.fetchone()[0]

        # Update matching number tiap jurnal item
        self.env.cr.execute("""
            UPDATE account_move_line
            SET full_reconcile_id = %s, matching_number = %s, reconciled = true
            WHERE id = ANY(%s)
        """, (full_reconcile_id, full_reconcile_id, line_ids))

        return move

    def _prepare_move_line(self, reconcile_ref):
        debit_account = self.bank_statement_id.journal_id.default_account_id

        if not debit_account:
            raise ValueError("Debit account not found.")

        move_lines = []

        # Add credit line
        total_amount_credit = 0.0
        total_amount_credit_in_currency = 0.0
        selected_move_lines = self.move_line_ids.filtered(lambda x: x.is_selected)
        for line in selected_move_lines:
            # total_amount_credit = total_amount_credit + line.amount_due_currency
            total_amount_credit = total_amount_credit + line.amount_due # credit selalu IDR (company's currency)
            total_amount_credit_in_currency = total_amount_credit_in_currency + line.amount_due_currency
            amount_due_in_currency = -abs(line.amount_due_currency) if line.amount_due_currency else 0.0
            move_lines.append(
                (0, 0, {
                    'account_id': line.account_id.id,
                    'name': line.name,
                    'debit': 0.0,
                    'credit': line.amount_due,
                    'currency_id': line.currency_id.id,
                    'amount_currency': amount_due_in_currency,
                }))

        # Add debit line
        total_amount_debit = total_amount_credit
        description = None
        first_loop = True

        selected_statement_lines = self.statement_line_ids.filtered(lambda x: x.is_selected)
        # ensure selected_statement_lines has only one record

        if len(selected_statement_lines) > 1:
            raise ValidationError("Please select only one bank statement line.")

        currency_id = None
        for line in selected_statement_lines:
            if not first_loop:
                break

            description = line.description
            line.reconcile_ref = reconcile_ref
            currency_id = line.currency_id
            first_loop = False

        move_lines.append(
            (0, 0, {
                'account_id': debit_account.id,
                'name': description,
                'debit': total_amount_debit,
                'credit': 0.0,
                'currency_id': currency_id.id,
                'amount_currency': total_amount_credit_in_currency,
            }))
            
        if(self.difference):
            difference_account_id = float(self.env['ir.config_parameter'].sudo().get_param('account_statement_reconcile.difference_account'))
            if not difference_account_id:
                raise ValidationError("Please set the Difference Account in Settings")

            line = {
                'account_id': difference_account_id,
                'name': "Rounding"
            }

            if(self.difference > 0):
                total_amount_debit = total_amount_debit - self.difference
                line.update({'debit': 0, 'credit': self.difference,})
            else:
                total_amount_credit = total_amount_credit - abs(self.difference)
                line.update({'debit': abs(self.difference), 'credit': 0,})

            move_lines.append((0, 0, line))

        if total_amount_credit != total_amount_debit:
            raise ValidationError(f"Total amount debit: {total_amount_debit} and credit: {total_amount_credit} do not match.")

        return move_lines
    
    def clear_bank_statement_selections(self):
        bank_statement_id = self.env.context.get('default_bank_statement_id')
        if bank_statement_id:
            self.env['bank.statement.line'].search([
                ('statement_id', '=', bank_statement_id)
            ]).write({'is_selected': False})

    def add_adjustment(self):
        step_order = ['step1', 'step2', 'step3']
        current_index = step_order.index(self.step)
        if current_index < len(step_order) - 1:
            self.step = step_order[current_index + 1]

        return {
            'name': 'Reconciliation Adjustment',
            'type': 'ir.actions.act_window',
            'res_model': self._name,
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
        }

    def close_adjustment(self):
        step_order = ['step1', 'step2', 'step3']
        current_index = step_order.index(self.step)
        if current_index > 0:
            self.step = step_order[current_index - 1]
        
        return {
            'name': 'Reconciliation Adjustment',
            'type': 'ir.actions.act_window',
            'res_model': self._name,
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
        }


class SystemTransactionWizardLine(models.TransientModel):
    _name = 'search.wizard.move.line'
    _description = 'System Transaction Line'

    wizard_id = fields.Many2one('bank.statement.search.wizard', string='Wizard')
    is_selected = fields.Boolean(string='Selected')
    move_line_id = fields.Many2one('account.move.line', string='Journal Item')
    # date = fields.Date(string='date', related='move_line_id.date')
    # ambil date journal entry karena deposit settlemtn banyak yang kosong datenya.
    # harusnya date journal items.
    date = fields.Date(string='date', related='move_line_id.move_id.date')
    move_id = fields.Many2one(string='Journal Entry', related='move_line_id.move_id')
    account_id = fields.Many2one(string='Account', related='move_line_id.account_id')
    journal_id = fields.Many2one(string='Journal Account', related='move_line_id.journal_id')
    name = fields.Char(string='Label', related='move_line_id.name')
    currency_id = fields.Many2one(related='move_line_id.currency_id')
    company_currency_id = fields.Many2one('res.currency', string="Company Currency", default=lambda self: self.env.company.currency_id.id)
    amount_due = fields.Monetary(string='Amount Due')
    amount_due_currency = fields.Monetary(string='Amount Due in Currency')
