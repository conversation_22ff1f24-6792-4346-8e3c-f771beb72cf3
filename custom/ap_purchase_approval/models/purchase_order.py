import base64
from base64 import urlsafe_b64decode as b64dec
from base64 import urlsafe_b64encode as b64enc
from datetime import date, datetime
from markupsafe import Markup
import logging
import urllib.parse
import zlib
from odoo import api, fields, models, SUPERUSER_ID
from odoo.exceptions import ValidationError
from odoo.http import request

import logging
_logger = logging.getLogger(__name__)
class PurchaseOrder(models.Model):
    _inherit = 'purchase.order'


    hierarchy_id = fields.Many2one('base.hierarchy', 'Approval Hierarchy')
    hierarchy_change_id = fields.Many2one('base.hierarchy', 'Approval Hierarchy Change')
    approval_history_ids = fields.One2many('purchase.order.approval',
                                           'purchase_id', 'Approval Histories')
    approval_history_change_ids = fields.One2many('purchase.order.change.approval',
                                           'purchase_id', 'Approval Histories')
    approval_history_detail_ids = fields.One2many('purchase.order.approval.detail',
                                           'purchase_id', 'Approval History Details')
    approval_history_detail_change_ids = fields.One2many('purchase.order.change.approval.detail',
                                           'purchase_id', 'Approval History Details')
    approval_message_ids = fields.One2many('purchase.order.message',
                                           'purchase_id', 'Message Histories')
    approval_message_change_ids = fields.One2many('purchase.order.change.message',
                                           'purchase_id', 'Message Histories')
    approval_reassign_ids = fields.One2many('purchase.order.reassign',
                                           'purchase_id', 'Reassing Employees')
    approval_reassign_change_ids = fields.One2many('purchase.order.change.reassign',
                                           'purchase_id', 'Reassing Employees')
    

    selected_approver_ids = fields.Many2many('hr.employee',
                                             string='Selected Approvers',
                                             compute='_compute_selected_approvers',
                                             store=True)
    # selected_approver_change_ids = fields.Many2many(
    #     'hr.employee',
    #     'employee_approver_po_change_rel',
    #     'purchase_order_change_id',
    #     'employee_change_id',
    #     string='Selected Approvers',
    #     compute='_compute_selected_approvers_change',
    #     store=True
    # )

        
    is_current_approver = fields.Boolean(string= 'Current Approver',
                                         compute='_compute_current_approver')
    # is_current_approver_change = fields.Boolean(string= 'Current Approver',
    #                                      compute='_compute_current_approver_change')
    is_reassign = fields.Boolean(string='Is Reassign',
                                         compute='_compute_reassign')
    is_reassign_change = fields.Boolean(string='Is Reassign',
                                         compute='_compute_reassign_change')
    is_admin = fields.Boolean(string='Is Admin',
                                         compute='check_administrator_setting')
    is_admin_change = fields.Boolean(string='Is Admin',
                                         compute='check_administrator_setting')
    
    def check_administrator_setting(self):
        for rec in self:
            group_admin = rec.sudo().env.ref('justif_approval.administrator_setting')
            if group_admin and group_admin.users and group_admin.users.filtered(lambda x: x.id == self.env.user.id):
                rec.is_admin = True
                rec.is_admin_change = True
            else:
                rec.is_admin = False
                rec.is_admin_change = False

    def button_action(self):
        ctx = self.env.context
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'purchase.hierarchy.wizard',
            'view_mode':'form',
            'views': [(self.env.ref('ap_purchase_approval.purchase_hierarchy_wizard_view_form').id, 'form')],
            'target': 'new',
            'context': {
                'readonly': True if not self.is_admin else False,
                'default_from_employee_id': self.env.user.employee_id.id if not self.is_admin else False,
                'default_purchase_id': self.id,
                'default_employee_domain': [('id', 'in', self.selected_approver_ids.ids)],
                'approve': ctx.get('approve'),
                'reject': ctx.get('reject'),
                'reassign': ctx.get('reassign')
            },
        }
    
    def button_approve(self):
        res = super(PurchaseOrder, self).button_approve()
        self.write({'new_state': 'pending_acknowledge'})
        return res

    def get_current_level(self, change=False):
        for rec in self:
            if not change:
                current_level = rec.approval_history_ids.filtered(lambda x: x.state == 'in_progress')
                if current_level:
                    return current_level[0]
                else:
                    return False
            else:
                current_level = rec.approval_history_change_ids.filtered(lambda x: x.state == 'in_progress')
                if current_level:
                    return current_level[0]
                else:
                    return False
            
    def get_employees(self, line=False, employees=False, employee_id=False, change=False):
        if line and line.approval_by == 'position_department':
            if line.approval_type == 'paralel' or (line.approval_type == 'serial' and not line.send_unit) or line.approval_type == 'voting':
                domain_emp = []
                domain_emp.append(('department_id', 'in', line.department_ids.ids))
                domain_emp.append(('job_id', 'in', line.job_ids.ids))

                if employee_id:
                    list_emp = []
                    list_emp.append(employee_id)
                    if not change:
                        check_reassign = self.approval_reassign_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                        if check_reassign:
                            employee_id2 = check_reassign[0].from_employee_id.id
                            list_emp.append(employee_id2)

                        domain_emp.append(('id', 'in', list_emp))
                    else:
                        check_reassign = self.approval_reassign_change_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                        if check_reassign:
                            employee_id2 = check_reassign[0].from_employee_id.id
                            list_emp.append(employee_id2)

                        domain_emp.append(('id', 'in', list_emp))

                if line.approval_employee_ids:
                    department_exist = line.approval_employee_ids.mapped('department_id')
                    domain_emp.append(('department_id', 'not in', department_exist.ids))

            else:
                domain_emp = []
                # domain_emp.append(('department_id', 'in', line.department_ids.ids))
                # domain_emp.append(('department_id', '=', self.requestor_id.department_id.id))
                # domain_emp.append(('job_id', 'in', line.job_ids.ids))

                dept_requestor = self.requestor_id.department_id
                domain_job = ('job_id', 'in', line.job_ids.ids)

                continue_check = True
                while continue_check:
                    domain_dept_requestor = ('department_id', '=', dept_requestor.id)
                    employee_check = self.env['hr.employee'].search([domain_dept_requestor, domain_job], limit=1)

                    if employee_check:
                        domain_emp.append(domain_dept_requestor)
                        domain_emp.append(domain_job)
                        continue_check = False
                    elif dept_requestor.parent_id:
                        dept_requestor = dept_requestor.parent_id
                    else:
                        domain_emp.append(('id', '=', False))
                        continue_check = False

                if employee_id:
                    list_emp = []
                    list_emp.append(employee_id)
                    if not change:
                        check_reassign = self.approval_reassign_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                        if check_reassign:
                            employee_id2 = check_reassign[0].from_employee_id.id
                            list_emp.append(employee_id2)

                        domain_emp.append(('id', 'in', list_emp))
                    else:
                        check_reassign = self.approval_reassign_change_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                        if check_reassign:
                            employee_id2 = check_reassign[0].from_employee_id.id
                            list_emp.append(employee_id2)

                        domain_emp.append(('id', 'in', list_emp))

                if line.approval_employee_ids:
                    department_exist = line.approval_employee_ids.mapped('department_id')
                    domain_emp.append(('department_id', 'not in', department_exist.ids))

            employees = self.env['hr.employee'].search(domain_emp)
            if not change:
                for emp in self.approval_reassign_ids:
                    if emp.from_employee_id in employees:
                        employees -= emp.from_employee_id
                        employees += emp.to_employee_id
            else:
                for emp in self.approval_reassign_change_ids:
                    if emp.from_employee_id in employees:
                        employees -= emp.from_employee_id
                        employees += emp.to_employee_id

        elif line and line.approval_by == 'job_level_department':   
            if line.approval_type == 'paralel' or (line.approval_type == 'serial' and not line.send_unit) or line.approval_type == 'voting':           
                domain_emp = []
                domain_emp.append(('department_id', 'in', line.department_ids.ids))
                domain_emp.append(('job_id.level', '=', line.job_level))

                if employee_id:
                    list_emp = []
                    list_emp.append(employee_id)
                    if not change:
                        check_reassign = self.approval_reassign_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                        if check_reassign:
                            employee_id2 = check_reassign[0].from_employee_id.id
                            list_emp.append(employee_id2)

                        domain_emp.append(('id', 'in', list_emp))
                    else:
                        check_reassign = self.approval_reassign_change_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                        if check_reassign:
                            employee_id2 = check_reassign[0].from_employee_id.id
                            list_emp.append(employee_id2)

                        domain_emp.append(('id', 'in', list_emp))

                if line.approval_employee_ids:
                    department_exist = line.approval_employee_ids.mapped('department_id')
                    domain_emp.append(('department_id', 'not in', department_exist.ids))

            else:
                domain_emp = []
                # domain_emp.append(('department_id', 'in', line.department_ids.ids))
                # domain_emp.append(('department_id', '=', self.requestor_id.department_id.id))
                # domain_emp.append(('job_id.level', '=', line.job_level))

                dept_requestor = self.requestor_id.department_id
                domain_level = ('job_id.level', '=', line.job_level)

                continue_check = True
                while continue_check:
                    domain_dept_requestor = ('department_id', '=', dept_requestor.id)
                    employee_check = self.env['hr.employee'].search([domain_dept_requestor, domain_level], limit=1)

                    if employee_check:
                        domain_emp.append(domain_dept_requestor)
                        domain_emp.append(domain_level)
                        continue_check = False
                    elif dept_requestor.parent_id:
                        dept_requestor = dept_requestor.parent_id
                    else:
                        domain_emp.append(('id', '=', False))
                        continue_check = False

                if employee_id:
                    list_emp = []
                    list_emp.append(employee_id)
                    if not change:
                        check_reassign = self.approval_reassign_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                        if check_reassign:
                            employee_id2 = check_reassign[0].from_employee_id.id
                            list_emp.append(employee_id2)

                        domain_emp.append(('id', 'in', list_emp))
                    else:
                        check_reassign = self.approval_reassign_change_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                        if check_reassign:
                            employee_id2 = check_reassign[0].from_employee_id.id
                            list_emp.append(employee_id2)

                        domain_emp.append(('id', 'in', list_emp))

                if line.approval_employee_ids:
                    department_exist = line.approval_employee_ids.mapped('department_id')
                    domain_emp.append(('department_id', 'not in', department_exist.ids))

            employees = self.env['hr.employee'].search(domain_emp)
            
            if not change:
                for emp in self.approval_reassign_ids:
                    if emp.from_employee_id in employees:
                        employees -= emp.from_employee_id
                        employees += emp.to_employee_id
            else:
                for emp in self.approval_reassign_change_ids:
                    if emp.from_employee_id in employees:
                        employees -= emp.from_employee_id
                        employees += emp.to_employee_id

        elif line and line.approval_by == 'position': 
            if line.approval_type == 'paralel' or (line.approval_type == 'serial' and not line.send_unit) or line.approval_type == 'voting':   
                domain_emp = []
                domain_emp.append(('job_id', 'in', line.job_ids.ids))

                if employee_id:
                    list_emp = []
                    list_emp.append(employee_id)
                    if not change:
                        check_reassign = self.approval_reassign_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                        if check_reassign:
                            employee_id2 = check_reassign[0].from_employee_id.id
                            list_emp.append(employee_id2)

                        domain_emp.append(('id', 'in', list_emp))
                    else:
                        check_reassign = self.approval_reassign_change_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                        if check_reassign:
                            employee_id2 = check_reassign[0].from_employee_id.id
                            list_emp.append(employee_id2)

                        domain_emp.append(('id', 'in', list_emp))

                # if line.approval_employee_ids:
                #     job_exist = line.approval_employee_ids.mapped('job_id')
                #     domain_emp.append(('job_id', 'not in', job_exist.ids))

            else:
                domain_emp = []
                # domain_emp.append(('department_id', '=', self.requestor_id.department_id.id))
                # domain_emp.append(('job_id', 'in', line.job_ids.ids))

                dept_requestor = self.requestor_id.department_id
                domain_job = ('job_id', 'in', line.job_ids.ids)

                continue_check = True
                while continue_check:
                    domain_dept_requestor = ('department_id', '=', dept_requestor.id)
                    employee_check = self.env['hr.employee'].search([domain_dept_requestor, domain_job], limit=1)

                    if employee_check:
                        domain_emp.append(domain_dept_requestor)
                        domain_emp.append(domain_job)
                        continue_check = False
                    elif dept_requestor.parent_id:
                        dept_requestor = dept_requestor.parent_id
                    else:
                        domain_emp.append(('id', '=', False))
                        continue_check = False

                if employee_id:
                    list_emp = []
                    list_emp.append(employee_id)
                    if not change:
                        check_reassign = self.approval_reassign_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                        if check_reassign:
                            employee_id2 = check_reassign[0].from_employee_id.id
                            list_emp.append(employee_id2)

                        domain_emp.append(('id', 'in', list_emp))
                    else:
                        check_reassign = self.approval_reassign_change_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                        if check_reassign:
                            employee_id2 = check_reassign[0].from_employee_id.id
                            list_emp.append(employee_id2)

                        domain_emp.append(('id', 'in', list_emp))

                # if line.approval_employee_ids:
                #     job_exist = line.approval_employee_ids.mapped('job_id')
                #     domain_emp.append(('job_id', 'not in', job_exist.ids))

            employees = self.env['hr.employee'].search(domain_emp)

            if line.approval_employee_ids:
                employees = employees - line.approval_employee_ids
            if not change:
                for emp in self.approval_reassign_ids:
                    if emp.from_employee_id in employees:
                        employees -= emp.from_employee_id
                        employees += emp.to_employee_id
            else:
                for emp in self.approval_reassign_change_ids:
                    if emp.from_employee_id in employees:
                        employees -= emp.from_employee_id
                        employees += emp.to_employee_id

        elif line and line.approval_by == 'department': 
            if line.approval_type == 'paralel' or (line.approval_type == 'serial' and not line.send_unit) or line.approval_type == 'voting': 
                domain_emp = []
                domain_emp.append(('department_id', 'in', line.department_ids.ids))

                if employee_id:
                    list_emp = []
                    list_emp.append(employee_id)
                    if not change:
                        check_reassign = self.approval_reassign_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                        if check_reassign:
                            employee_id2 = check_reassign[0].from_employee_id.id
                            list_emp.append(employee_id2)

                        domain_emp.append(('id', 'in', list_emp))
                    else:
                        check_reassign = self.approval_reassign_change_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                        if check_reassign:
                            employee_id2 = check_reassign[0].from_employee_id.id
                            list_emp.append(employee_id2)

                        domain_emp.append(('id', 'in', list_emp))

                if line.approval_employee_ids:
                    department_exist = line.approval_employee_ids.mapped('department_id')
                    domain_emp.append(('department_id', 'not in', department_exist.ids))

            else:
                domain_emp = []
                # domain_emp.append(('department_id', '=', self.requestor_id.department_id.id))
                # domain_emp.append(('department_id', 'in', line.department_ids.ids))

                dept_requestor = self.requestor_id.department_id
                # domain_job = ('job_id', 'in', line.job_ids.ids)

                continue_check = True
                while continue_check:
                    domain_dept_requestor = ('department_id', '=', dept_requestor.id)
                    employee_check = self.env['hr.employee'].search([domain_dept_requestor], limit=1)

                    if employee_check:
                        domain_emp.append(domain_dept_requestor)
                        # domain_emp.append(domain_job)
                        continue_check = False
                    elif dept_requestor.parent_id:
                        dept_requestor = dept_requestor.parent_id
                    else:
                        domain_emp.append(('id', '=', False))
                        continue_check = False

                if employee_id:
                    list_emp = []
                    list_emp.append(employee_id)
                    if not change:
                        check_reassign = self.approval_reassign_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                    else:
                        check_reassign = self.approval_reassign_change_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                    if check_reassign:
                        employee_id2 = check_reassign[0].from_employee_id.id
                        list_emp.append(employee_id2)

                    domain_emp.append(('id', 'in', list_emp))

                if line.approval_employee_ids:
                    department_exist = line.approval_employee_ids.mapped('department_id')
                    domain_emp.append(('department_id', 'not in', department_exist.ids))

            employees = self.env['hr.employee'].search(domain_emp)
            if not change:
                for emp in self.approval_reassign_ids:
                    if emp.from_employee_id in employees:
                        employees -= emp.from_employee_id
                        employees += emp.to_employee_id
            else:
                for emp in self.approval_reassign_change_ids:
                    if emp.from_employee_id in employees:
                        employees -= emp.from_employee_id
                        employees += emp.to_employee_id


        elif line and line.approval_by == 'employee': 
            employees = line.employee_ids

            # if employee_id:
            #     employee = self.env['hr.employee'].browse(employee_id)
            #     employees = employees - employee

            if employee_id:
                list_emp = []
                list_emp.append(employee_id)
                if not change:
                    check_reassign = self.approval_reassign_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                else:
                    check_reassign = self.approval_reassign_change_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                if check_reassign:
                    employee_id2 = check_reassign[0].from_employee_id.id
                    list_emp.append(employee_id2)

                # employee = self.env['hr.employee'].browse(list_emp)
                employees = employees.filtered(lambda x: x.id in list_emp)

            if line.approval_employee_ids:
                employees = employees - line.approval_employee_ids

            if not change:
                for emp in self.approval_reassign_ids:
                    if emp.from_employee_id in employees:
                        employees -= emp.from_employee_id
                        employees += emp.to_employee_id
            else:
                for emp in self.approval_reassign_change_ids:
                    if emp.from_employee_id in employees:
                        employees -= emp.from_employee_id
                        employees += emp.to_employee_id

        elif line and line.approval_by == 'job_level':
            if line.approval_type == 'paralel' or (line.approval_type == 'serial' and not line.send_unit) or line.approval_type == 'voting':
                domain_emp = []
                domain_emp.append(('job_id.level', '=', line.job_level))

                if employee_id:
                    list_emp = []
                    list_emp.append(employee_id)
                    if not change:
                        check_reassign = self.approval_reassign_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                    else:
                        check_reassign = self.approval_reassign_change_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                    if check_reassign:
                        employee_id2 = check_reassign[0].from_employee_id.id
                        list_emp.append(employee_id2)

                    domain_emp.append(('id', 'in', list_emp))
            else:
                domain_emp = []
                # domain_emp.append(('department_id', '=', self.requestor_id.department_id.id))
                # domain_emp.append(('job_id.level', '=', line.job_level))

                dept_requestor = self.requestor_id.department_id
                domain_level = ('job_id.level', '=', line.job_level)

                continue_check = True
                while continue_check:
                    domain_dept_requestor = ('department_id', '=', dept_requestor.id)
                    employee_check = self.env['hr.employee'].search([domain_dept_requestor, domain_level], limit=1)

                    if employee_check:
                        domain_emp.append(domain_dept_requestor)
                        domain_emp.append(domain_level)
                        continue_check = False
                    elif dept_requestor.parent_id:
                        dept_requestor = dept_requestor.parent_id
                    else:
                        domain_emp.append(('id', '=', False))
                        continue_check = False

                if employee_id:
                    list_emp = []
                    list_emp.append(employee_id)
                    if not change:
                        check_reassign = self.approval_reassign_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                    else:
                        check_reassign = self.approval_reassign_change_ids.filtered(lambda x: x.to_employee_id.id == employee_id)
                    if check_reassign:
                        employee_id2 = check_reassign[0].from_employee_id.id
                        list_emp.append(employee_id2)

                    domain_emp.append(('id', 'in', list_emp))

            employees = self.env['hr.employee'].search(domain_emp)

            if line.approval_employee_ids:
                employees = employees - line.approval_employee_ids

            if not change:
                for emp in self.approval_reassign_ids:
                    if emp.from_employee_id in employees:
                        employees -= emp.from_employee_id
                        employees += emp.to_employee_id
            else:
                for emp in self.approval_reassign_change_ids:
                    if emp.from_employee_id in employees:
                        employees -= emp.from_employee_id
                        employees += emp.to_employee_id

        if line and line.approval_type == 'serial' and line.approval_employee_ids:
            employees = False
            
        return employees

    @api.depends('approval_history_ids.approval_employee_ids', 'approval_reassign_ids', 'approval_history_ids', 'state', 'approval_history_detail_ids', 'approval_history_change_ids.approval_employee_ids', 'approval_reassign_change_ids', 'approval_history_change_ids', 'approval_history_detail_change_ids')
    def _compute_selected_approvers(self):
        """ compute function to get selected approvers based on hierarchy_id """
        for rec in self:
            if not rec.is_change_po:
                line = rec.get_current_level()
                employees = rec.get_employees(line)
            else:
                line = rec.get_current_level(change=True)
                employees = rec.get_employees(line, change=True)

            rec.selected_approver_ids = employees

    def _check_is_complete_approval_line(self, line, change=False):
        for rec in self:
            if line.approval_type == 'voting':
                is_complete = True if line.total_voting_point >= line.voting_point else False
            else:
                if not change:
                    is_complete = not bool(rec.get_employees(line, True))
                else:
                    is_complete = not bool(rec.get_employees(line, True, change=True))

            return is_complete
        
    def _not_complete_approval(self, change=False):
        for rec in self:
            if not change:
                is_complete = bool(rec.approval_history_ids.filtered(lambda x: x.state in ['in_progress', 'approve', 'reject']))
            else:
                is_complete = bool(rec.approval_history_change_ids.filtered(lambda x: x.state in ['in_progress', 'approve', 'reject']))
            return is_complete

    def _approve(self, approver=False):
        for rec in self:
            today_date = datetime.now()
            if rec.is_change_po:
                line = rec.get_current_level(change=True)
            else:
                line = rec.get_current_level()
            approver_employee = approver or self.env.user.employee_id

            if not rec.is_change_po:
                check_reassign = rec.approval_reassign_ids.filtered(lambda x: x.to_employee_id == approver_employee)
                if check_reassign:
                    # check_reassign = check_reassign[0]
                    from_employee_id = False

                    for reassign in check_reassign:
                        check_employee_exist_reassing = rec.get_employees(line, employee_id=reassign.from_employee_id.id)
                        if check_employee_exist_reassing:
                            from_employee_id = reassign.from_employee_id

                    if from_employee_id:
                        line.write({'approval_employee_ids': [(4, from_employee_id.id)]})
                        line.total_voting_point += from_employee_id.voting_point

                    # check details approval normal
                    check_detail = rec.approval_history_detail_ids.filtered(lambda x: x.employee_id == approver_employee and not x.employee_note)
                    if check_detail:
                        vals = {
                            'employee_state': 'approve',
                            'employee_note': self.env.context.get('note'),
                            'employee_date': today_date
                        }
                        check_detail.write(vals)

                    # check details approval reassign
                    check_detail = rec.approval_history_detail_ids.filtered(lambda x: x.reassign_employee_id == approver_employee and not x.reassign_employee_note)
                    if check_detail:
                        vals = {
                            'reassign_employee_state': 'approve',
                            'reassign_employee_note': self.env.context.get('note'),
                            'reassign_employee_date': today_date
                        }
                        check_detail.write(vals)

                    check_employee_exist_reassing = rec.get_employees(line, employee_id=approver_employee.id)
                    if check_employee_exist_reassing:
                        line.write({'approval_employee_ids': [(4, approver_employee.id)]})
                        line.total_voting_point += approver_employee.voting_point
                    
                else:
                    line.write({'approval_employee_ids': [(4, approver_employee.id)]})
                    line.total_voting_point += approver_employee.voting_point

                    # check details approval normal
                    check_detail = rec.approval_history_detail_ids.filtered(lambda x: x.employee_id == approver_employee and not x.employee_note)
                    if check_detail:
                        vals = {
                            'employee_state': 'approve',
                            'employee_note': self.env.context.get('note'),
                            'employee_date': today_date
                        }
                        check_detail.write(vals)

                    # check details approval reassign
                    check_detail = rec.approval_history_detail_ids.filtered(lambda x: x.reassign_employee_id == approver_employee and not x.reassign_employee_note)
                    if check_detail:
                        vals = {
                            'reassign_employee_state': 'approve',
                            'reassign_employee_note': self.env.context.get('note'),
                            'reassign_employee_date': today_date
                        }
                        check_detail.write(vals)

                if not approver:
                    msg = {
                        'employee_id': approver_employee.id,
                        'date': today_date,
                        'state': 'approve',
                        'note': self.env.context.get('note')
                    }
                    self.approval_message_ids = [(0, 0, msg)]

                level = line.level
                line_check = rec.approval_history_ids.filtered(lambda x: x.level == level)
                while line_check:
                    check_employee_exist = rec.get_employees(line_check, employee_id=approver_employee.id)
                    if check_employee_exist:
                        check_reassign = rec.approval_reassign_ids.filtered(lambda x: x.to_employee_id == approver_employee)
                        if check_reassign:
                            # check_reassign = check_reassign[0]
                            from_employee_id = False

                            for reassign in check_reassign:
                                check_employee_exist_reassing = rec.get_employees(line_check, employee_id=reassign.from_employee_id.id)
                                if check_employee_exist_reassing:
                                    from_employee_id = reassign.from_employee_id

                            if from_employee_id:
                                line_check.write({'approval_employee_ids': [(4, from_employee_id.id)]})
                                line_check.total_voting_point += from_employee_id.voting_point

                            # check details approval normal
                            check_detail = rec.approval_history_detail_ids.filtered(lambda x: x.employee_id == approver_employee and not x.employee_state)
                            if check_detail:
                                vals = {
                                    'employee_state': 'approve',
                                    'employee_note': self.env.context.get('note'),
                                    'employee_date': today_date
                                }
                                check_detail.write(vals)

                            # check details approval reassign
                            check_detail = rec.approval_history_detail_ids.filtered(lambda x: x.reassign_employee_id == approver_employee and not x.reassign_employee_note)
                            if check_detail:
                                vals = {
                                    'reassign_employee_state': 'approve',
                                    'reassign_employee_note': self.env.context.get('note'),
                                    'reassign_employee_date': today_date
                                }
                                check_detail.write(vals)

                            check_employee_exist_reassing = rec.get_employees(line_check, employee_id=approver_employee.id)
                            if check_employee_exist_reassing:
                                line_check.write({'approval_employee_ids': [(4, approver_employee.id)]})
                                line_check.total_voting_point += approver_employee.voting_point
                            
                        else:
                            line_check.write({'approval_employee_ids': [(4, approver_employee.id)]})
                            line_check.total_voting_point += approver_employee.voting_point

                            # check details approval normal
                            check_detail = rec.approval_history_detail_ids.filtered(lambda x: x.employee_id == approver_employee and not x.employee_state)
                            if check_detail:
                                vals = {
                                    'employee_state': 'approve',
                                    'employee_note': self.env.context.get('note'),
                                    'employee_date': today_date
                                }
                                check_detail.write(vals)

                            # check details approval reassign
                            check_detail = rec.approval_history_detail_ids.filtered(lambda x: x.reassign_employee_id == approver_employee and not x.reassign_employee_note)
                            if check_detail:
                                vals = {
                                    'reassign_employee_state': 'approve',
                                    'reassign_employee_note': self.env.context.get('note'),
                                    'reassign_employee_date': today_date
                                }
                                check_detail.write(vals)

                    before_line = bool(rec.approval_history_ids.filtered(lambda x: x.level < line_check.level and x.state not in ['approve', 'reject']))
                    if rec._check_is_complete_approval_line(line_check) and not before_line:
                        line_check.write({'state': 'approve'})

                        next_level = line_check.level + 1
                        next_line = rec.approval_history_ids.filtered(lambda x: x.level == next_level)
                        if next_line:
                            next_line.state = 'in_progress'

                            # send email
                            emails = False
                            employees = rec.get_employees(next_line)
                            if employees:
                                emails = employees.filtered(lambda x: x.work_email).mapped('work_email')
                            if emails:
                                email_to = ', '.join(emails)
                                template = rec._get_approval_template()

                                web_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
                                url_root = '%s/' % web_url  
                                link = '%sodoo/purchase-orders/%s' % (url_root, self.id)
                                rec.with_context(link=link).send_email(email_to, template)

                    # next check
                    next_level_check = line_check.level + 1
                    line_check = rec.approval_history_ids.filtered(lambda x: x.level == next_level_check)

                check_note_exist = rec.approval_history_detail_ids.filtered(lambda x: (x.reassign_employee_id == approver_employee or x.employee_id == approver_employee) and (x.employee_note or x.reassign_employee_note))
                if check_note_exist:
                    check_note_exist = check_note_exist[0]
                    note = check_note_exist.employee_note or check_note_exist.reassign_employee_note

                    # check details approval normal
                    check_detail = rec.approval_history_detail_ids.filtered(lambda x: x.employee_id == approver_employee and not x.employee_state)
                    if check_detail:
                        vals = {
                            'employee_note': note,
                        }
                        check_detail.write(vals)

                    # check details approval reassign
                    check_detail = rec.approval_history_detail_ids.filtered(lambda x: x.reassign_employee_id == approver_employee and not x.reassign_employee_note)
                    if check_detail:
                        vals = {
                            'reassign_employee_note': note,
                        }
                        check_detail.write(vals)
                        
                if not rec._not_complete_approval():
                    rec.button_approve() # main function for approve
                    rec.action_approve_change_order()
                    _logger.info("Purchase Order %s has been apasdasdasdproved", rec.name)

                    # send email
                    emails = rec.requestor_id.work_email
                    if emails:
                        email_to = emails
                        template = rec._get_info_template()
                        message = f"Purchase Order number {rec.name} has been approved"

                        web_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
                        url_root = '%s/' % web_url  
                        link = '%sodoo/purchase-orders/%s' % (url_root, self.id)
                        rec.with_context(message=message, link=link).send_email(email_to, template)
            else:
                check_reassign = rec.approval_reassign_change_ids.filtered(lambda x: x.to_employee_id == approver_employee)
                if check_reassign:
                    # check_reassign = check_reassign[0]
                    from_employee_id = False

                    for reassign in check_reassign:
                        check_employee_exist_reassing = rec.get_employees(line, employee_id=reassign.from_employee_id.id, change=True)
                        if check_employee_exist_reassing:
                            from_employee_id = reassign.from_employee_id

                    if from_employee_id:
                        line.write({'approval_employee_ids': [(4, from_employee_id.id)]})
                        line.total_voting_point += from_employee_id.voting_point

                    # check details approval normal
                    check_detail = rec.approval_history_detail_change_ids.filtered(lambda x: x.employee_id == approver_employee and not x.employee_note)
                    if check_detail:
                        vals = {
                            'employee_state': 'approve',
                            'employee_note': self.env.context.get('note'),
                            'employee_date': today_date
                        }
                        check_detail.write(vals)

                    # check details approval reassign
                    check_detail = rec.approval_history_detail_change_ids.filtered(lambda x: x.reassign_employee_id == approver_employee and not x.reassign_employee_note)
                    if check_detail:
                        vals = {
                            'reassign_employee_state': 'approve',
                            'reassign_employee_note': self.env.context.get('note'),
                            'reassign_employee_date': today_date
                        }
                        check_detail.write(vals)

                    check_employee_exist_reassing = rec.get_employees(line, employee_id=approver_employee.id, change=True)
                    if check_employee_exist_reassing:
                        line.write({'approval_employee_ids': [(4, approver_employee.id)]})
                        line.total_voting_point += approver_employee.voting_point
                    
                else:
                    line.write({'approval_employee_ids': [(4, approver_employee.id)]})
                    line.total_voting_point += approver_employee.voting_point

                    # check details approval normal
                    check_detail = rec.approval_history_detail_change_ids.filtered(lambda x: x.employee_id == approver_employee and not x.employee_note)
                    if check_detail:
                        vals = {
                            'employee_state': 'approve',
                            'employee_note': self.env.context.get('note'),
                            'employee_date': today_date
                        }
                        check_detail.write(vals)

                    # check details approval reassign
                    check_detail = rec.approval_history_detail_change_ids.filtered(lambda x: x.reassign_employee_id == approver_employee and not x.reassign_employee_note)
                    if check_detail:
                        vals = {
                            'reassign_employee_state': 'approve',
                            'reassign_employee_note': self.env.context.get('note'),
                            'reassign_employee_date': today_date
                        }
                        check_detail.write(vals)

                if not approver:
                    msg = {
                        'employee_id': approver_employee.id,
                        'date': today_date,
                        'state': 'approve',
                        'note': self.env.context.get('note')
                    }
                    self.approval_message_change_ids = [(0, 0, msg)]

                level = line.level
                line_check = rec.approval_history_change_ids.filtered(lambda x: x.level == level)
                while line_check:
                    check_employee_exist = rec.get_employees(line_check, employee_id=approver_employee.id, change=True)
                    if check_employee_exist:
                        check_reassign = rec.approval_reassign_change_ids.filtered(lambda x: x.to_employee_id == approver_employee)
                        if check_reassign:
                            # check_reassign = check_reassign[0]
                            from_employee_id = False

                            for reassign in check_reassign:
                                check_employee_exist_reassing = rec.get_employees(line_check, employee_id=reassign.from_employee_id.id, change=True)
                                if check_employee_exist_reassing:
                                    from_employee_id = reassign.from_employee_id

                            if from_employee_id:
                                line_check.write({'approval_employee_ids': [(4, from_employee_id.id)]})
                                line_check.total_voting_point += from_employee_id.voting_point

                            # check details approval normal
                            check_detail = rec.approval_history_detail_change_ids.filtered(lambda x: x.employee_id == approver_employee and not x.employee_state)
                            if check_detail:
                                vals = {
                                    'employee_state': 'approve',
                                    'employee_note': self.env.context.get('note'),
                                    'employee_date': today_date
                                }
                                check_detail.write(vals)

                            # check details approval reassign
                            check_detail = rec.approval_history_detail_change_ids.filtered(lambda x: x.reassign_employee_id == approver_employee and not x.reassign_employee_note)
                            if check_detail:
                                vals = {
                                    'reassign_employee_state': 'approve',
                                    'reassign_employee_note': self.env.context.get('note'),
                                    'reassign_employee_date': today_date
                                }
                                check_detail.write(vals)

                            check_employee_exist_reassing = rec.get_employees(line_check, employee_id=approver_employee.id, change=True)
                            if check_employee_exist_reassing:
                                line_check.write({'approval_employee_ids': [(4, approver_employee.id)]})
                                line_check.total_voting_point += approver_employee.voting_point
                            
                        else:
                            line_check.write({'approval_employee_ids': [(4, approver_employee.id)]})
                            line_check.total_voting_point += approver_employee.voting_point

                            # check details approval normal
                            check_detail = rec.approval_history_detail_change_ids.filtered(lambda x: x.employee_id == approver_employee and not x.employee_state)
                            if check_detail:
                                vals = {
                                    'employee_state': 'approve',
                                    'employee_note': self.env.context.get('note'),
                                    'employee_date': today_date
                                }
                                check_detail.write(vals)

                            # check details approval reassign
                            check_detail = rec.approval_history_detail_change_ids.filtered(lambda x: x.reassign_employee_id == approver_employee and not x.reassign_employee_note)
                            if check_detail:
                                vals = {
                                    'reassign_employee_state': 'approve',
                                    'reassign_employee_note': self.env.context.get('note'),
                                    'reassign_employee_date': today_date
                                }
                                check_detail.write(vals)

                    before_line = bool(rec.approval_history_change_ids.filtered(lambda x: x.level < line_check.level and x.state not in ['approve', 'reject']))
                    if rec._check_is_complete_approval_line(line_check, change=True) and not before_line:
                        line_check.write({'state': 'approve'})

                        next_level = line_check.level + 1
                        next_line = rec.approval_history_change_ids.filtered(lambda x: x.level == next_level)
                        if next_line:
                            next_line.state = 'in_progress'

                            # send email
                            emails = False
                            employees = rec.get_employees(next_line, change=True)
                            if employees:
                                emails = employees.filtered(lambda x: x.work_email).mapped('work_email')
                            if emails:
                                email_to = ', '.join(emails)
                                template = rec._get_approval_template()

                                web_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
                                url_root = '%s/' % web_url  
                                link = '%sodoo/purchase-orders/%s' % (url_root, self.id)
                                rec.with_context(link=link).send_email(email_to, template)

                    # next check
                    next_level_check = line_check.level + 1
                    line_check = rec.approval_history_change_ids.filtered(lambda x: x.level == next_level_check)

                check_note_exist = rec.approval_history_detail_change_ids.filtered(lambda x: (x.reassign_employee_id == approver_employee or x.employee_id == approver_employee) and (x.employee_note or x.reassign_employee_note))
                if check_note_exist:
                    check_note_exist = check_note_exist[0]
                    note = check_note_exist.employee_note or check_note_exist.reassign_employee_note

                    # check details approval normal
                    check_detail = rec.approval_history_detail_change_ids.filtered(lambda x: x.employee_id == approver_employee and not x.employee_state)
                    if check_detail:
                        vals = {
                            'employee_note': note,
                        }
                        check_detail.write(vals)

                    # check details approval reassign
                    check_detail = rec.approval_history_detail_change_ids.filtered(lambda x: x.reassign_employee_id == approver_employee and not x.reassign_employee_note)
                    if check_detail:
                        vals = {
                            'reassign_employee_note': note,
                        }
                        check_detail.write(vals)
                        
                if rec._not_complete_approval(change=True):
                    rec.button_approve()
                    rec.action_approve_change_order() # main function for approve
                    _logger.info("Purchase Order %s has been approved", rec.name)

                    # send email
                    emails = rec.requestor_id.work_email
                    if emails:
                        email_to = emails
                        template = rec._get_info_template()
                        message = f"Purchase Order number {rec.name} has been approved"

                        web_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
                        url_root = '%s/' % web_url  
                        link = '%sodoo/purchase-orders/%s' % (url_root, self.id)
                        rec.with_context(message=message, link=link).send_email(email_to, template)

    def _reject(self):
        for rec in self:
            if rec.is_change_po:
                line = rec.get_current_level(change=True)
            else:
                line = rec.get_current_level()
            line.write({'state': 'reject'})

            today_date = datetime.now()

            # check details approval normal
            if not rec.is_change_po:
                check_detail = rec.approval_history_detail_ids.filtered(lambda x: x.employee_id == self.env.user.employee_id)
                if check_detail:
                    vals = {
                        'employee_state': 'reject',
                        'employee_note': self.env.context.get('note'),
                        'employee_date': today_date
                    }
                    check_detail.write(vals)

                # check details approval reassign
                check_detail = rec.approval_history_detail_ids.filtered(lambda x: x.reassign_employee_id == self.env.user.employee_id)
                if check_detail:
                    vals = {
                        'reassign_employee_state': 'reject',
                        'reassign_employee_note': self.env.context.get('note'),
                        'reassign_employee_date': today_date
                    }
                    check_detail.write(vals)

                msg = {
                    'employee_id': self.env.user.employee_id.id,
                    'date': today_date,
                    'state': 'reject',
                    'note': self.env.context.get('note')
                }
                rec.approval_message_ids = [(0, 0, msg)]

                rec.action_reject()

                # send email
                emails = rec.requestor_id.work_email
                if emails:
                    email_to = emails
                    template = rec._get_info_template()
                    message = f"Purchase Order number {rec.name} has been rejected"

                    web_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
                    url_root = '%s/' % web_url  
                    link = '%sodoo/purchase-orders/%s' % (url_root, self.id)
                    rec.with_context(message=message, link=link).send_email(email_to, template)
            else:
                check_detail = rec.approval_history_detail_change_ids.filtered(lambda x: x.employee_id == self.env.user.employee_id)
                if check_detail:
                    vals = {
                        'employee_state': 'reject',
                        'employee_note': self.env.context.get('note'),
                        'employee_date': today_date
                    }
                    check_detail.write(vals)

                # check details approval reassign
                check_detail = rec.approval_history_detail_change_ids.filtered(lambda x: x.reassign_employee_id == self.env.user.employee_id)
                if check_detail:
                    vals = {
                        'reassign_employee_state': 'reject',
                        'reassign_employee_note': self.env.context.get('note'),
                        'reassign_employee_date': today_date
                    }
                    check_detail.write(vals)

                msg = {
                    'employee_id': self.env.user.employee_id.id,
                    'date': today_date,
                    'state': 'reject',
                    'note': self.env.context.get('note')
                }
                rec.approval_message_change_ids = [(0, 0, msg)]

                rec.action_reject()

                # send email
                emails = rec.requestor_id.work_email
                if emails:
                    email_to = emails
                    template = rec._get_info_template()
                    message = f"Purchase Order number {rec.name} has been rejected"

                    web_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
                    url_root = '%s/' % web_url  
                    link = '%sodoo/purchase-orders/%s' % (url_root, self.id)
                    rec.with_context(message=message, link=link).send_email(email_to, template)

    def _compute_current_approver(self):
        """ compute function to get current approver """
        for rec in self:
            is_current_approver = False
            if self.env.user.employee_id in rec.selected_approver_ids:
                is_current_approver = True

            rec.is_current_approver = is_current_approver

    # def _compute_current_approver_change(self):
    #     """ compute function to get current approver """
    #     for rec in self:
    #         is_current_approver = False
    #         if self.env.user.employee_id in rec.selected_approver_change_ids:
    #             is_current_approver = True

    #         rec.is_current_approver = is_current_approver
    
    def _compute_current_requestor(self):
        for rec in self:
            is_requestor = False
            if self.env.user.employee_id == rec.requestor_id:
                is_requestor = True

            rec.is_requestor = is_requestor

    def _compute_reassign(self):
        for rec in self:
            is_reassign = False
            check_reassign = rec.approval_reassign_ids.filtered(lambda x: x.to_employee_id == self.env.user.employee_id)
            if check_reassign:
                is_reassign = True

            rec.is_reassign = is_reassign 

    def _compute_reassign_change(self):
        for rec in self:
            is_reassign = False
            check_reassign = rec.approval_reassign_change_ids.filtered(lambda x: x.to_employee_id == self.env.user.employee_id)
            if check_reassign:
                is_reassign = True

            rec.is_reassign = is_reassign 

    def _assign_approval(self, change=False):
        """ helper function to assign approval hierarchy to history """
        
        if not self.is_change_po:
            check_hierarchy = self.env['base.hierarchy'].sudo().search([('model_id.model', '=', 'purchase.order')]) \
                                                        .filtered(lambda x: x.minimum_amount <= self.amount_total <= x.maximum_amount \
                                                        and self.requestor_id.department_id.id in x.department_ids.ids)       
        else:
            check_hierarchy = self.env['base.hierarchy'].sudo().search([('model_id.model', '=', 'purchase.order'), ('is_change_po', '=', True)]) \
                                                        .filtered(lambda x: x.minimum_amount <= self.amount_total <= x.maximum_amount \
                                                        and self.requestor_id.department_id.id in x.department_ids.ids)
        
        _logger.info(f"Check hierarchy: {check_hierarchy}")
        if not check_hierarchy:
            raise ValidationError('Cannot found hierarchy for Purchase Order. Please check your configuration!')
        
        self.hierarchy_id = check_hierarchy[0].id if not change else False
        self.hierarchy_change_id = check_hierarchy[0].id if change else False
        if not change:
            hierarchy = self.hierarchy_id
        else:
            hierarchy = self.hierarchy_change_id
        
        if not self.is_change_po:
            if hierarchy: 
                lines = [(5, 0, 0)]
                lines_detail = [(5, 0, 0)]
                self.approval_reassign_ids = [(5, 0, 0)]

                for line in hierarchy.line_ids.sorted(key=lambda x: x.level):
                    state = 'in_progress' if line.level == 1 else False

                    data = {
                        'level': line.level,
                        'send_unit': line.send_unit,
                        'approval_by': line.approval_by,
                        'department_ids': line.department_ids,
                        'job_ids': line.job_ids,
                        'job_level': line.job_level,
                        'employee_ids': line.employee_ids,
                        'approval_type': line.approval_type,
                        'voting_point': line.voting_point,
                        'state': state,
                        'hierarchy_department_ids': hierarchy.department_ids.ids,
                    }
                    lines.append((0, 0, data))

                self.approval_history_ids = lines

                for line in self.approval_history_ids:
                    employees = self.get_employees(line)
                    for employee in employees:
                        vals_detail = {
                            'level': line.level,
                            'employee_id': employee.id,
                            'reassign_employee_id': False,
                            'reassign_employee_date': False,
                            'reassign_employee_state': False,
                            'reassign_employee_note': False
                        }
                        lines_detail.append((0, 0, vals_detail))

                self.approval_history_detail_ids = lines_detail

                # check complete hierarchy
                self.check_hiearchy()
        else:
            if hierarchy: 
                lines = [(5, 0, 0)]
                lines_detail = [(5, 0, 0)]
                self.approval_reassign_change_ids = [(5, 0, 0)]

                for line in hierarchy.line_ids.sorted(key=lambda x: x.level):
                    state = 'in_progress' if line.level == 1 else False

                    data = {
                        'level': line.level,
                        'send_unit': line.send_unit,
                        'approval_by': line.approval_by,
                        'department_ids': line.department_ids,
                        'job_ids': line.job_ids,
                        'job_level': line.job_level,
                        'employee_ids': line.employee_ids,
                        'approval_type': line.approval_type,
                        'voting_point': line.voting_point,
                        'state': state,
                        'hierarchy_department_ids': hierarchy.department_ids.ids,
                    }
                    lines.append((0, 0, data))

                self.approval_history_change_ids = lines

                for line in self.approval_history_change_ids:
                    employees = self.get_employees(line, change=True)
                    for employee in employees:
                        vals_detail = {
                            'level': line.level,
                            'employee_id': employee.id,
                            'reassign_employee_id': False,
                            'reassign_employee_date': False,
                            'reassign_employee_state': False,
                            'reassign_employee_note': False
                        }
                        lines_detail.append((0, 0, vals_detail))

                self.approval_history_detail_change_ids = lines_detail

                # check complete hierarchy
                self.check_hiearchy()

    def check_hiearchy(self):
        if not self.is_change_po:
            base_hierarchy = self.approval_history_ids.mapped('level')
            detail_hierarchy = self.approval_history_detail_ids.mapped('level')
        else:
            base_hierarchy = self.approval_history_change_ids.mapped('level')
            detail_hierarchy = self.approval_history_detail_change_ids.mapped('level')

        if not base_hierarchy or not detail_hierarchy:
            raise ValidationError('Not complete hierarchy, please check your configuration!')
        if set(detail_hierarchy) != set(base_hierarchy):
            raise ValidationError('Not complete hierarchy, please check your configuration!')

    def action_request_approval(self):
        for rec in self:
            rec._assign_approval()

            emails = False
            if not rec.is_change_po:
                line = rec.get_current_level()
                employees = rec.get_employees(line)
                if employees:
                    emails = employees.filtered(lambda x: x.work_email).mapped('work_email')
                if emails:
                    email_to = ', '.join(emails)
                    template = rec._get_approval_template()

                    web_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
                    url_root = '%s/' % web_url  
                    link = '%sodoo/purchase-orders/%s' % (url_root, self.id)
                    rec.with_context(link=link).send_email(email_to, template)
            else:
                line = rec.get_current_level(change=True)
                employees = rec.get_employees(line, change=True)
                if employees:
                    emails = employees.filtered(lambda x: x.work_email).mapped('work_email')
                if emails:
                    email_to = ', '.join(emails)
                    template = rec._get_approval_template()

                    web_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
                    url_root = '%s/' % web_url  
                    link = '%sodoo/purchase-orders/%s' % (url_root, self.id)
                    rec.with_context(link=link).send_email(email_to, template)

    def send_email(self, email_to, template):
        """ function to send info email to requestor """
        message = self.env.context.get('message')
        link = self.env.context.get('link')
        mail_to = email_to
        try:
            template_id = self.sudo().env.ref('%s' % template)
        except ValueError:
            template_id = False

        # different_date = self._context.get('different_date', 0)

        if mail_to and template_id:
            # template_id.attachment_ids = attachment
            template_id.with_context(
                mail_to=mail_to,
                message=message,
                link=link
            ).send_mail(self.id, force_send=True)

    def _get_approval_template(self):
        """ function to return mail template for approval """
        return 'ap_purchase_approval.mail_purchase_order_approval'

    def _get_info_template(self):
        """ function to return mail template for info """
        return 'ap_purchase_approval.mail_purchase_order_info'


    def button_approve_confirm(self):
        self.button_confirm()
        self.action_request_approval()

    def action_return(self):
        res = super(PurchaseOrder, self).action_return()
        if not self.is_change_po:
            current_level_to_return = self.approval_history_ids.filtered(lambda x: x.state == 'in_progress')
        else:
            current_level_to_return = self.approval_history_change_ids.filtered(lambda x: x.state == 'in_progress')
        if current_level_to_return:
            current_level_to_return.state = 'return'

    def action_resubmit(self):
        if not self.is_change_po:
            current_level_to_return = self.approval_history_ids.filtered(lambda x: x.state == 'return')
        else:
            current_level_to_return = self.approval_history_change_ids.filtered(lambda x: x.state == 'return')
        if current_level_to_return:
            current_level_to_return.state = 'in_progress'

        self.button_confirm()

    # def action_reset_draft(self):
    #     res = super(AccountJustification, self).action_reset_draft()

    #     if self.approval_history_ids:
    #         self.create_history_log_approval()
    #     return res
    
    # def get_state_label(self, line, field, data):
    #     selection_dict = dict(line._fields[field].selection)
    #     label = selection_dict.get(data, '')
    #     return label

    # def create_history_log_approval(self):
    #     table_html = Markup("""
    #         History Approval (SET TO DRAFT): <br>
    #         <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse;">
    #             <thead>
    #                 <tr>
    #                     <th>Level</th>
    #                     <th>Approver Name</th>
    #                     <th>Approver Position</th>
    #                     <th>Approval Status</th>
    #                     <th>Approval Date</th>
    #                     <th>Approval Note</th>
    #                     <th>Reassign To</th>
    #                     <th>Approval Date</th>
    #                     <th>Approval Status</th>
    #                     <th>Approval Note</th>
    #                     <th>Minimum Amount</th>
    #                 </tr>
    #             </thead>
    #             <tbody>
    #     """)

    #     for line in self.approval_history_ids:
    #         employee_state = self.get_state_label(line, 'employee_state', line.employee_state) if line.employee_state else ''
    #         reassign_employee_state = self.get_state_label(line, 'reassign_employee_state', line.reassign_employee_state) if line.reassign_employee_state else ''
            
    #         table_html += Markup(f"""
    #             <tr>
    #                 <td>{line.level}</td>
    #                 <td>{line.employee_id.name or ''}</td>
    #                 <td>{line.employee_job_id.name or ''}</td>
    #                 <td>{employee_state}</td>
    #                 <td>{line.employee_date or ''}</td>
    #                 <td>{line.employee_note or ''}</td>
    #                 <td>{line.reassign_employee_id.name or ''}</td>
    #                 <td>{line.reassign_employee_date or ''}</td>
    #                 <td>{reassign_employee_state}</td>
    #                 <td>{line.reassign_employee_note or ''}</td>
    #                 <td>{line.minimum_amount or ''}</td>
    #             </tr>
    #         """)

    #     table_html += Markup("""
    #             </tbody>
    #         </table>
    #     """)

    #     self.message_post(
    #         body=table_html
    #     )
