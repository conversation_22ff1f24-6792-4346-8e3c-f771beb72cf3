from odoo import models, fields, api


class PurchaseOrderChangeApproval(models.Model):
    _name = 'purchase.order.change.approval'
    _inherit = 'approval.history.mixin'
    _description = 'Purchase Order Change Approval'

    purchase_id = fields.Many2one('purchase.order', 'Purchase Order',
                               ondelete='cascade')

    reassign_employee_ids = fields.Many2many(
        'hr.employee',
        'employee_reassign_hierarchy_purchase_change_rel',
        'purchase_order_change_reassign_approval_id',
        'employee_id',
        string='Reassign Employees'
    )


    employee_ids = fields.Many2many(
        'hr.employee',
        'employee_approval_hierarchy_purchase_change_rel',  # tetap
        'purchase_order_change_approval_id',
        'employee_id',
        string='Employees'
    )

    approval_employee_ids = fields.Many2many(
        'hr.employee',
        'approval_employee_hierarchy_change_rel',
        'purchase_order_change_approval_approval_id',
        'approval_employee_id',
        string='Approval Employees'
    )
    hierarchy_department_ids = fields.Many2many(
        'hr.department',
        'approval_change_hierarchy_department_rel',
        'purchase_order_change_approval_id',
        'department_id',
        string='Hierarchy Units')
    state = fields.Selection([
        ('in_progress', 'In Progress'),
        ('approve', 'Approved'),
        ('reject', 'Rejected'),
        ('return', 'Return')
    ], 'Approval Status', copy=False)


class PurchaseOrderChangeApprovalDetail(models.Model):
    _name = 'purchase.order.change.approval.detail'
    _inherit = 'approval.history.detail.mixin'
    _description = 'Purchase Order Change Approval'

    purchase_id = fields.Many2one('purchase.order', 'Purchase Order',
                               ondelete='cascade')

    

class PurchaseOrderReassign(models.Model):
    _name = 'purchase.order.change.reassign'
    _description = 'Purchase Order Reassign'
    

    purchase_id = fields.Many2one('purchase.order', 'Purchase Order',
                               ondelete='cascade')
    from_employee_id = fields.Many2one('hr.employee', string='From Employee')
    to_employee_id = fields.Many2one('hr.employee', string='To Employee') 
