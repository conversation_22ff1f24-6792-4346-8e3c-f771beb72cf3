<odoo>
    <data>
        <record id="purchase_order_loa_form" model="ir.ui.view">
            <field name="name">purchase.order.loa.form</field>
            <field name="model">purchase.order</field>
            <field name="inherit_id" ref="purchase.purchase_order_form"/>
            <field name="arch" type="xml">
                <!-- add button to do action -->
                <xpath expr="//header" position="inside">
                    <button name="button_action" type="object" string="Approve" class="btn btn-success" 
                        invisible="new_state != 'pending_approval' or not is_current_approver"
                        context="{'approve': True}" />
                    <button name="button_action" type="object" string="Reject" class="btn-danger"
                        invisible="new_state != 'pending_approval' or not is_current_approver"
                        context="{'reject': True}" />
                    <button name="button_action" type="object" string="Reassign" class="btn btn-success" 
                        invisible="new_state != 'pending_approval' or (not is_current_approver and not is_admin)"
                        context="{'reassign': True}" />
                    <button name="action_resubmit" type="object" string="Submit" class="btn btn-success" 
                        invisible="new_state != 'return'" />
                    <button name="button_approve_confirm" string="Submit" type="object" invisible="new_state not in ['draft']" class="oe_highlight"/>
                </xpath>

                <!-- add approval_history_ids in new page inside notebook -->
                <xpath expr="//notebook" position="inside">
                    <page name="history" string="Approvals">
                        <field name="is_current_approver" invisible="1"/>
                        <field name="selected_approver_ids" widget="many2many_tags" invisible="1"/>
                        <field name="approval_history_ids" readonly="1" force_save='1' />
                    </page>
                    <page string="Approval Details" name="history_detail">
                        <field name="approval_history_detail_ids" >
                            <list editable="bottom" create="0" edit="0" delete="0">
                                <field name="level" readonly="1" force_save='1' />
                                <field name="employee_id" readonly="1" force_save='1' />
                                <field name="employee_state" readonly="1" force_save='1' />
                                <field name="employee_date" readonly="1" force_save='1' />
                                <field name="employee_note" readonly="1" force_save='1' />
                                <field name="reassign_employee_id" readonly="1" force_save='1' />
                                <field name="reassign_employee_state" readonly="1" force_save='1' />
                                <field name="reassign_employee_date" readonly="1" force_save='1' />
                                <field name="reassign_employee_note" readonly="1" force_save='1' />
                            </list>
                        </field>
                    </page>
                    <page name="approval_message" string="Message Details">
                        <field name="approval_message_ids" readonly="1" force_save='1' />
                    </page>
                    <page name="history_change" string="Change PO Approval" invisible="is_change_po != True">
                        <!-- <field name="is_current_approver_change" invisible="1"/>
                        <field name="selected_approver_change_ids" widget="many2many_tags" invisible="1"/> -->
                        <field name="approval_history_change_ids" readonly="1" force_save='1' />
                    </page>
                    <page string="Change Approval Details" name="history_detail_change" invisible="is_change_po != True">
                        <field name="approval_history_detail_change_ids" >
                            <list editable="bottom" create="0" edit="0" delete="0">
                                <field name="level" readonly="1" force_save='1' />
                                <field name="employee_id" readonly="1" force_save='1' />
                                <field name="employee_state" readonly="1" force_save='1' />
                                <field name="employee_date" readonly="1" force_save='1' />
                                <field name="employee_note" readonly="1" force_save='1' />
                                <field name="reassign_employee_id" readonly="1" force_save='1' />
                                <field name="reassign_employee_state" readonly="1" force_save='1' />
                                <field name="reassign_employee_date" readonly="1" force_save='1' />
                                <field name="reassign_employee_note" readonly="1" force_save='1' />
                            </list>
                        </field>
                    </page>
                    <page name="approval_message_change" string="Change Approval Details" invisible="1">
                        <field name="approval_message_change_ids" readonly="1" force_save='1' />
                    </page>
                </xpath>

                <!-- put hierarchy_id and current_user_id after currency_id -->
                <xpath expr="//field[@name='date_order']" position="after">
                    <field name="hierarchy_id" readonly="1" force_save='1' />
                    <field name="hierarchy_change_id" readonly="1" force_save='1' />
                </xpath>


                <!-- <xpath expr="//field[@name='requestor_id']" position="attributes">
					<attribute name="required">True</attribute>
				</xpath> -->

            </field>
        </record>

        <!-- <record id="purchase_order_view_search" model="ir.ui.view">
            <field name="name">purchase.order.search</field>
            <field name="model">purchase.order</field>
            <field name="priority">1</field>
            <field name="arch" type="xml">
                <search>
                    <separator/>
                    <filter name="my_approvals" string="My Approvals" domain="[('selected_approver_ids', 'in', [current_employee_id])]"/>
                    <separator/>
                </search>
            </field>
        </record> -->

        <record id="purchase.purchase_form_action" model="ir.actions.act_window">
            <field name="context">{'search_default_my_approvals': 1}</field>
        </record>

        <record id="view_purchase_order_filter" model="ir.ui.view">
            <field name="name">view.purchase.order.filter</field>
            <field name="model">purchase.order</field>
            <field name="inherit_id" ref="purchase.purchase_order_view_search"/>
            <field name="arch" type="xml">
                <xpath expr="//filter[@name='starred']" position="after">
                    <separator/>
                    <filter name="my_approvals" string="My Approvals" domain="[('selected_approver_ids.user_id', '=', uid)]" />
                    <separator/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
