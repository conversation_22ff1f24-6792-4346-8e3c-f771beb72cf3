<odoo>
    <record id="inherit_purchase_order_form" model="ir.ui.view">
        <field name="name">inherit.purchase.order.form</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_form"/>
        <field name="arch" type="xml">

            <xpath expr="//field[@name='state']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='state']" position="after">
                <field name="count_order_cancel" invisible="1" />
                <field name="new_state" widget="statusbar" statusbar_visible="draft,pending_approval,pending_acknowledge,canceled,open,reject,closed" readonly="1"/>
            </xpath>

            <xpath expr="//button[@name='button_cancel']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//button[@name='button_cancel']" position="after">
                 <button name="button_cancel_new" string="Cancel" type="object" data-hotkey="x" invisible="new_state not in ('open', 'draft') or count_order_cancel > 0"/>
                 <button name="button_close" string="Close" type="object" data-hotkey="x" invisible="new_state != 'open'"/>
            </xpath>

        </field>
    </record>

</odoo>