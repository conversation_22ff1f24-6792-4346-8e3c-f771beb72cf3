import logging
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta

from odoo import SUPERUSER_ID, api, fields, models, _
from odoo.exceptions import ValidationError, UserError
from odoo.tools.misc import formatLang
from odoo.tools.float_utils import float_compare
from odoo.addons import decimal_precision as dp
import logging

_logger = logging.getLogger(__name__)

NEW_STATE = [
    ('draft', "Draft"),
    ('pending_approval', "Pending Approval"),
    ('pending_acknowledge', "Pending Acknowledge"),
    ('return', "Return"),
    ('canceled', "Canceled"),
    ('open', "Open"),
    ('return', "Returned"),
    ('reject', "Rejected"),
    ('closed', "Closed"),
    ('done', "Purchase Order"),
]

class PurchaseOrder(models.Model):
    _inherit = "purchase.order"

    def _default_payment_term_30_days(self):
        term_line = self.env['account.payment.term.line'].search([
            ('nb_days', '=', 30)
        ], limit=1)
        return term_line.payment_id.id if term_line and term_line.payment_id else False

    def _get_pic_id(self):
        employee = self.env['hr.employee'].search([('user_id', '=', self.env.user.id)], limit=1)
        employee_id = employee.id
        return employee_id

    new_state = fields.Selection(
        selection=NEW_STATE,
        string="New Status",
        readonly=False, 
        copy=False, 
        index=True,
        default='draft',
        compute='_set_close_when_invoiced', 
        store=True,
        help="The new status of the purchase order, such as 'Draft', 'pending approval', or other states.")
    purchase_request_id = fields.Many2one(
        comodel_name="purchase.request",
        string="PR Number",
        index=True, copy=False,
        help="Referensi ke dokumen PR (Purchase Request) yang terkait.")
    is_rfq = fields.Boolean(string='Is RFQ', copy=False,
                            help="Centang jika PO ini berhubungan dengan RFQ terkait.")
    rfq_id = fields.Many2one(
        comodel_name="purchase.order",
        string="RFQ Number",
        index=True, copy=False,
        help="Referensi ke dokumen RFQ (Request for Quotation) yang terkait.")
    bidding_rfq_id = fields.Many2one(
        comodel_name="bidding.rfq",
        domain="[('state', '=', 'submit')]",
        string="RFQ Number",
        index=True, copy=False,
        help="Referensi ke dokumen RFQ (Request for Quotation) yang terkait.")
    is_dpl = fields.Boolean(string='Is DPL', copy=False,
                            help="Centang jika PO ini berhubungan dengan DPL terkait.")
    dpl_id = fields.Many2one(
        comodel_name="purchase.dpl",
        string="DPL Number",
        index=True, copy=False,
        help="Referensi ke dokumen DPL yang terkait.")
    partner_contact_id = fields.Many2one('res.partner', string='Vendor Contact', domain="[('parent_id', '=', partner_id), ('parent_id', '!=', False)]", help="You can find a vendor contact.")
    email_contact = fields.Char(string='Email Contact', copy=False,
                            help="Email address of the vendor's contact.")
    street = fields.Char(string="Alamat", readonly=True)
    street2 = fields.Char(string="Alamat2", readonly=True)
    zip = fields.Char(string="Kode Pos", readonly=True)
    city = fields.Char(string="Kota", readonly=True)
    state_id = fields.Many2one('res.country.state', string='Provinsi', readonly=True)
    country_id = fields.Many2one('res.country', string='Negara', readonly=True)
    pr_pic_id = fields.Many2one(
        comodel_name="hr.employee",
        string="PR PIC",
        copy=False,
        # default=_get_pic_id,
        help="User yang bertanggung jawab sebagai PIC (Person in Charge) pada PR ini.")
    requestor_id = fields.Many2one(
        comodel_name="hr.employee",
        string="Requested by",
        copy=False,
        help="User yang request pada PR ini.")
    group_id = fields.Many2one(
        comodel_name="hr.department",
        string="Group",
        index=True, copy=False,
        context={'hierarchical_naming': False},
        help="Grup divisi tempat pengaju PR.")
    unit_id = fields.Many2one(
        comodel_name="hr.department",
        string="Unit",
        index=True, copy=False,
        context={'hierarchical_naming': False},
        help="Unit kerja tempat pengaju PR.")
    is_po_acknowledge = fields.Boolean(string='Is PO Acknowledge', copy=False)
    transaction_date = fields.Date(string='Transaction_date', copy=False, default=fields.Date.context_today)
    date_purchase = fields.Date('PO Date', index=True, copy=False)
    latest_delivery_date = fields.Date(string='Latest Delivery Date', copy=False, compute='_compute_latest_delivery_date', store=True)
    validity_period = fields.Date(
        string='PO Validity Period',
        copy=False
    )
    is_agreement = fields.Boolean(string='Is MPA', copy=False,
                            help="Centang jika PO ini berhubungan dengan MPA terkait.")
    agreement_id = fields.Many2one(
        comodel_name="bidding.agreement",
        domain="[('state', '=', 'submit'), ('partner_id', '=', partner_id)]",
        string="MPA Number",
        index=True, copy=False,
        help="Referensi ke dokumen MPA (Master Purchase Agreement) jika ada.")
    rkap_id = fields.Many2one(
        comodel_name="account.budget.post",
        string="RKAP",
        index=True, copy=False,
        help="Referensi ke RKAP (Rencana Kerja dan Anggaran Perusahaan).")
    budget_line_id = fields.Many2one('crossovered.budget.lines', 
        string='Financial Pooling')
    rkap_code = fields.Char(string='Kode RKAP', copy=False,
                            help="Kode yang digunakan pada RKAP untuk kegiatan ini.")
    rkap_type_id = fields.Many2one(
        comodel_name="account.rkap.type",
        string="RKAP Type",
        index=True, copy=False,
        help="Tipe anggaran pada RKAP.")
    rkap_category_id = fields.Many2one(
        comodel_name="account.rkap.category",
        string="RKAP Category",
        index=True, copy=False,
        help="Kategori anggaran pada RKAP.")
    accrue_expense = fields.Boolean(string='Accrue Expense', copy=False,
                                    help="Centang jika biaya akan diakui secara akrual.")
    project = fields.Char(string='Project', copy=False,
                          help="Nama atau kode proyek yang terkait dengan PR ini.")
    buyer_id = fields.Many2one(
        comodel_name='hr.employee',
        string="Buyer",
        copy=False,
        help="Buyer yang ditugaskan untuk menangani PR ini.")
    payment_term_id = fields.Many2one('account.payment.term', 'Payment Terms', 
        domain="['|', ('company_id', '=', False), ('company_id', '=', company_id)]",
        default=lambda self: self._default_payment_term_30_days()
        )

    delivery_location_id = fields.Many2one(
        comodel_name="stock.location",
        string="Delivery Location",
        help="Specify the delivery location for this transaction.",
        copy=False)
    purchase_assign_id = fields.Many2one(
        comodel_name='hr.employee',
        string="Purchase Assign",
        copy=False)
    receipt_state = fields.Char(string='Receipt Status', copy=False)
    bill_state = fields.Char(string='Bill Status', copy=False)
    change_order_versi = fields.Char(string='Change Order Version', copy=False)

    is_bidding_rfq_readonly = fields.Boolean(string="Is RFQ Readonly", compute="_compute_bidding_rfq_readonly")
    bidding_rfq_id_readonly = fields.Boolean(string="RFQ Number Readonly", compute="_compute_bidding_rfq_readonly")
    is_agreement_readonly = fields.Boolean(string="Is MPA Readonly", compute="_compute_agreement_readonly")
    agreement_id_readonly = fields.Boolean(string="MPA Number Readonly", compute="_compute_agreement_readonly")
    is_dpl_readonly = fields.Boolean(string="Is DPL Readonly", compute="_compute_dpl_readonly")
    dpl_id_readonly = fields.Boolean(string="DPL Number Readonly", compute="_compute_dpl_readonly")
    order_attachment_ids = fields.One2many(
        comodel_name="purchase.order.attachment",
        inverse_name="order_id",
        string="Attachments",
        help="List of attached documents related to this purchase order.")
    
    pr_type = fields.Selection([
        ('rfq', 'RFQ'),
        ('rfp', 'MPA'),
        ('manual', 'Manual'),
    ], string='PR Type', related='purchase_request_id.pr_type')

    total_manajemen_fee = fields.Monetary(
        string="Total Management Fee",
        currency_field='currency_id',
        # compute="_compute_total_manajemen_fee",
        store=True,
        help="Jumlah total biaya manajemen dari baris yang ditandai sebagai manajemen fee.",
        copy=False)
    
    employee_id = fields.Many2one('hr.employee', string='Submitter Name', default=lambda self: self.env.user.employee_id)
    supplier_reacknowledge = fields.Boolean(string='Supplier Re-acknowledge', default=False)
    is_change_po = fields.Boolean(string='Is Change PO', default=False)  
    gr_count = fields.Integer(string="GR Count", compute="_compute_gr_count")

    def _compute_gr_count(self):
        for order in self:
            order.gr_count = self.env['stock.picking'].search_count([
                ('purchase_dua_id', '=', order.id)
            ])

    def action_change_order(self):
        for record in self:
            if record.new_state == 'open':  
                record.write({
                    'new_state': 'draft',
                    'is_change_po': True
                })

    def action_approve_change_order(self):
        for rec in self:
            if rec.new_state == 'pending_approval':
                if rec.supplier_reacknowledge:
                    rec.new_state = 'pending_acknowledge'
                    rec.is_change_po = False
                    rec.change_order_versi = str(int(rec.change_order_versi or '0') + 1)
                else:
                    rec.new_state = 'open'
                    rec.is_change_po = False
                    rec.change_order_versi = str(int(rec.change_order_versi or '0') + 1)
                
    def action_set_to_draft(self):
        for record in self:
            record.write({'new_state': 'draft'})
        
    def action_reject(self):
        self.write({'new_state': 'reject'})
    
    # @api.constrains('invoice_status')
    # def _set_close_when_invoiced(self):
        # for rec in self:
            # if rec.invoice_status == 'invoiced':
                # rec.new_state = 'closed'
    
    @api.depends('invoice_status', 'order_line.qty_received')
    def _set_close_when_invoiced(self):
        for rec in self:
            rec.new_state = 'draft'  # Default fallback

            # Defer logic if not ready or empty
            if not rec.id or not rec.order_line:
                continue

            try:
                precision = self.env['decimal.precision'].precision_get('Product Unit of Measure')
            except Exception:
                precision = 2

            all_received = all(
                float_compare(line.qty_received, line.product_qty, precision_digits=precision) >= 0
                for line in rec.order_line
            )

            if rec.invoice_status == 'invoiced' and all_received:
                rec.new_state = 'closed'
            else:
                rec.new_state = 'open'

    def auto_close_pr(self):
        for record in self.search([('new_state', '=', 'open')]):
            record.purchase_request_id.state = 'done'

    @api.onchange("order_line")
    def _compute_total_manajemen_fee(self):
        for record in self:
            record.total_manajemen_fee = sum(
                line.price_subtotal for line in record.order_line if line.is_manajemen_fee
            )
            print(record.total_manajemen_fee, 'manaajemen')
            for line in record.order_line:
                if line.is_percentage and record.total_manajemen_fee:
                    manajemen_fee = (line.product_qty / 100.0) * record.total_manajemen_fee
                    if line.product_qty != 0:
                        line.price_unit = manajemen_fee / line.product_qty
                        print(line.price_unit, 'oyyyyyyyyyyyy')

    # @api.onchange('purchase_request_id')
    # def _onchange_order_line(self):
    #     order_line = [(5, 0, 0)]
    #     if self.purchase_request_id:
    #         if self.purchase_request_id.line_ids:
    #             if self.purchase_request_id.pr_type == 'manual':
    #                 if self.bidding_rfq_id:
    #                     line_ids = self.bidding_rfq_id.order_line
    #                 elif self.agreement_id:
    #                     line_ids = self.agreement_id.line_ids
    #             else:
    #                 line_ids = self.purchase_request_id.line_ids
    #             for line in line_ids:
    #                 if line.product_qty > 0 and line.price_unit > 0:
    #                     new_line_vals = {
    #                         'order_id': self.id,
    #                         'purchase_request_line_id': line.id,
    #                         'name': line.product_tmpl_id.name,
    #                         'product_tmpl_id': line.product_tmpl_id.id,
    #                         'product_id': line.product_id.id,
    #                         'product_category_id': line.product_category_id.id,
    #                         'description': line.name,
    #                         'new_currency_id': line.new_currency_id.id,
    #                         'currency_rate': line.currency_rate,
    #                         'product_qty': line.product_qty,
    #                         'product_uom': line.product_uom_id.id,
    #                         'is_percentage': line.is_percentage,
    #                         'price_unit': line.price_unit,
    #                         'subtotal_currency': line.subtotal_currency,
    #                         'price_subtotal': line.estimated_cost,
    #                         'latest_delivery_date': line.end_date,
    #                         'is_manajemen_fee': line.is_manajemen_fee,
    #                     }
    #                     order_line.append((0, 0, new_line_vals))
    #                 else:
    #                     raise UserError(_("Quantity Product dan Price Product pada Purchase Request tidak boleh 0"))
    #         else:
    #             raise UserError(_("Tidak ada product pada Purchase Request"))
    #         self.order_line = order_line
    #     else:
    #         self.order_line = order_line

    @api.depends('order_line.latest_delivery_date')
    def _compute_latest_delivery_date(self):
        for rec in self:
            latest_delivery_date = [d for d in rec.order_line.mapped('latest_delivery_date') if d]
            rec.latest_delivery_date = max(latest_delivery_date) if latest_delivery_date else False
    
    @api.depends('purchase_request_id.pr_type', 'new_state')
    def _compute_bidding_rfq_readonly(self):
        for order in self:
            manual_rfq = False
            if order.purchase_request_id.pr_type == 'manual':
                bidding_id_rfq = self.env['bidding.bidding'].search([('is_purchase_request', '=', True),
                    ('purchase_request_id', '=', order.purchase_request_id.id),
                    ('bidding_type', '=', 'rfq')])
                bidding_id_rfp = self.env['bidding.bidding'].search([('is_purchase_request', '=', True),
                    ('purchase_request_id', '=', order.purchase_request_id.id),
                    ('bidding_type', '=', 'rfp')])
                if bidding_id_rfq and not bidding_id_rfp:
                    manual_rfq = True
            order.is_bidding_rfq_readonly = (
                (manual_rfq) or
                (not order.purchase_request_id) or
                (order.purchase_request_id.pr_type == 'rfq') or
                (order.new_state not in ('draft', 'return'))
            )
            order.bidding_rfq_id_readonly = (
                (not order.purchase_request_id) or
                (order.purchase_request_id.pr_type == 'rfq') or
                (order.new_state not in ('draft', 'return'))
            )
    
    @api.depends('purchase_request_id.pr_type', 'new_state')
    def _compute_agreement_readonly(self):
        for order in self:
            manual_rfp = False
            if order.purchase_request_id.pr_type == 'manual':
                bidding_id_rfq = self.env['bidding.bidding'].search([('is_purchase_request', '=', True),
                    ('purchase_request_id', '=', order.purchase_request_id.id),
                    ('bidding_type', '=', 'rfq')])
                bidding_id_rfp = self.env['bidding.bidding'].search([('is_purchase_request', '=', True),
                    ('purchase_request_id', '=', order.purchase_request_id.id),
                    ('bidding_type', '=', 'rfp')])
                if not bidding_id_rfq and bidding_id_rfp:
                    manual_rfp = True
            order.is_agreement_readonly = (
                (manual_rfp) or
                (not order.purchase_request_id) or
                (order.purchase_request_id.pr_type == 'rfp') or
                (order.new_state not in ('draft', 'return'))
            )
            order.agreement_id_readonly = (
                (not order.purchase_request_id) or
                (order.purchase_request_id.pr_type == 'rfp') or
                (order.new_state not in ('draft', 'return'))
            )
    
    @api.depends('purchase_request_id.pr_type', 'new_state')
    def _compute_dpl_readonly(self):
        for order in self:
            order.is_dpl_readonly = (
                (not order.purchase_request_id) or
                (order.purchase_request_id.is_dpl) or
                (order.new_state not in ('draft', 'return'))
            )
            order.dpl_id_readonly = (
                (not order.purchase_request_id) or
                (order.purchase_request_id.dpl_id) or
                (order.new_state not in ('draft', 'return'))
            )

    def button_confirm(self):
        for order in self:
            if order.order_line.filtered(lambda x: x.price_unit == 0):
                raise ValidationError('Unit Price tidak boleh 0. Mohon hapus line tersebut terlebih dahulu')
            if order.new_state == 'draft' or order.new_state == 'return':
                for order_line in order.order_line:
                    if order_line.product_qty == 0:
                        raise UserError(_("Quantity Product pada Purchase Order tidak boleh 0, mohon hapus terlebih dahulu"))
                order.new_state = 'pending_approval'
                return
            if order.new_state == 'pending_approval':
                order.new_state = 'pending_acknowledge'
                return
            if order.new_state == 'pending_acknowledge':
                order.new_state = 'open'
                order.supplier_reacknowledge = False
                res = super(PurchaseOrder, self).button_confirm()
                if not order.date_purchase:
                    order.date_purchase = fields.Datetime.now()
                    date_val = fields.Datetime.now() + relativedelta(days=30)
                    order.validity_period = date_val
                else:
                    date_val = order.date_purchase + relativedelta(days=30)
                    order.validity_period = date_val
                return res
    
    def button_draft(self):
        for order in self:
            order.write({'new_state': 'draft'})
        res = super(PurchaseOrder, self).button_draft()
        return res

    # @api.onchange('purchase_request_id')
    # def onchange_rkap(self):
    #     self.rkap_id = self.purchase_request_id.rkap_id.id
    #     self.rkap_code = self.purchase_request_id.rkap_code
    #     self.rkap_type_id = self.purchase_request_id.rkap_type_id.id
    #     self.rkap_category_id = self.purchase_request_id.rkap_category_id.id

    @api.onchange('partner_id')
    def onchange_partner_address(self):
        self.street = self.partner_id.street
        self.street2 = self.partner_id.street2
        self.zip = self.partner_id.zip
        self.city = self.partner_id.city
        self.state_id = self.partner_id.state_id.id
        self.country_id = self.partner_id.country_id.id
        self.payment_term_id = self.partner_id.payment_term_id.id

    @api.onchange('partner_contact_id')
    def onchange_partner_contact(self):
        self.email_contact = self.partner_contact_id.email

    @api.onchange('requestor_id')
    def onchange_requested_by(self):
        self.requester_user_id = self.requestor_id.user_id.id

    @api.onchange('purchase_request_id')
    def onchange_purchase_request(self):
        print('wokeeeeeeeeeeeeeeeeeeeeee1111111')
        fields_to_reset = [
            'description', 'is_rfq', 'is_agreement', 'is_dpl', 'bidding_rfq_id',
            'agreement_id', 'dpl_id', 'partner_id', 'partner_contact_id',
            'email_contact', 'street', 'street2', 'zip', 'city', 'state_id',
            'country_id', 'pr_pic_id', 'requestor_id', 'unit_id', 'group_id',
            'is_po_acknowledge', 'date_purchase', 'latest_delivery_date',
            'rkap_id', 'rkap_code', 'rkap_category_id', 'rkap_type_id',
            'accrue_expense', 'buyer_id'
        ]
        for field in fields_to_reset:
            setattr(self, field, False)

        self.order_line = [(5, 0, 0)]
        line_ids = False

        bidding_rfq_line_ids = False
        agreement_line_ids = False

        if self.purchase_request_id:
            self.pr_pic_id = self.purchase_request_id.pic_id.id
            self.requestor_id = self.purchase_request_id.requestor_id.id
            self.unit_id = self.purchase_request_id.unit_id.id
            self.group_id = self.purchase_request_id.hr_group_id.id
            self.rkap_id = self.purchase_request_id.rkap_id.id
            self.rkap_code = self.purchase_request_id.rkap_code
            self.rkap_type_id = self.purchase_request_id.rkap_type_id.id
            self.rkap_category_id = self.purchase_request_id.rkap_category_id.id
            self.buyer_id = self.purchase_request_id.buyer_id.id
            if self.purchase_request_id.is_dpl and self.purchase_request_id.dpl_id:
                self.is_dpl = self.purchase_request_id.is_dpl
                self.dpl_id = self.purchase_request_id.dpl_id


            if self.purchase_request_id.pr_type == 'manual' and not self.purchase_request_id.is_dpl and not self.purchase_request_id.dpl_id:
                bidding_ids = self.env['bidding.bidding'].search([('is_purchase_request', '=', True),
                    ('purchase_request_id', '=', self.purchase_request_id.id),
                    ('state', 'not in', ['draft', 'cancel'])])
                
                if bidding_ids and all(bidding_id.bidding_type == 'rfq' for bidding_id in bidding_ids):
                    self.is_rfq = True
                    if len(bidding_ids) == 1:
                        bidding_rfq_ids = self.env['bidding.rfq'].search([('bidding_id', '=', bidding_ids.id),
                            ('state', '=', 'submit')])
                        if len(bidding_rfq_ids) == 1:
                            rfq_exist_in_po_id = self.env['purchase.order'].search([('bidding_rfq_id', '=', bidding_rfq_ids.id)], limit=1)
                            if rfq_exist_in_po_id:
                                raise UserError(_("Nomor RFQ '%s' sudah menjadi Purchase Order pada nomor PO '%s'.") % (bidding_rfq_ids.name, rfq_exist_in_po_id.name))
                            self.bidding_rfq_id = bidding_rfq_ids.id
                            self.partner_id = bidding_rfq_ids.partner_id.id
                            bidding_rfq_line_ids = bidding_rfq_ids.order_line
                
                if bidding_ids and all(bidding_id.bidding_type == 'rfp' for bidding_id in bidding_ids):
                    self.is_agreement = True
                    if len(bidding_ids) == 1:
                        agreement_ids = self.env['bidding.agreement'].search([('bidding_id', '=', bidding_ids.id),
                            ('state', '=', 'submit')])
                        if len(agreement_ids) == 1:
                            # mpa_exist_in_po_id = self.env['purchase.order'].search([('agreement_id', '=', agreement_ids.id)], limit=1)
                            # if mpa_exist_in_po_id:
                            #     raise UserError(_("Nomor MPA '%s' sudah menjadi Purchase Order pada nomor PO '%s'.") % (agreement_ids.name, mpa_exist_in_po_id.name))
                            self.agreement_id = agreement_ids.id
                            self.partner_id = agreement_ids.partner_id.id
                            agreement_line_ids = agreement_ids.line_ids
            
            # if self.purchase_request_id.pr_type == 'manual' and self.purchase_request_id.is_dpl and self.purchase_request_id.dpl_id:
            #     dpl_exist_in_po_id = self.env['purchase.order'].search([('dpl_id', '=', self.purchase_request_id.dpl_id.id)], limit=1)
            #     if dpl_exist_in_po_id:
            #         raise UserError(_("Nomor DPL '%s' sudah menjadi Purchase Order pada nomor PO '%s'.") % (self.purchase_request_id.dpl_id.name, dpl_exist_in_po_id.name))
            #     self.is_dpl = True
            #     self.dpl_id = self.purchase_request_id.dpl_id.id
            
            if self.purchase_request_id.pr_type == 'rfq':
                rfq_exist_in_po_id = self.env['purchase.order'].search([('bidding_rfq_id', '=', self.purchase_request_id.rfq_id.id)], limit=1)
                if rfq_exist_in_po_id:
                    raise UserError(_("Nomor RFQ '%s' sudah menjadi Purchase Order pada nomor PO '%s'.") % (self.purchase_request_id.rfq_id.name, rfq_exist_in_po_id.name))
                self.is_rfq = True
                self.bidding_rfq_id = self.purchase_request_id.rfq_id.id
                self.requestor_id = self.purchase_request_id.rfq_id.bidding_id.requestor_id.id
            
            if self.purchase_request_id.pr_type == 'rfp':
                # mpa_exist_in_po_id = self.env['purchase.order'].search([('agreement_id', '=', self.purchase_request_id.agreement_id.id)], limit=1)
                # if mpa_exist_in_po_id:
                #     raise UserError(_("Nomor MPA '%s' sudah menjadi Purchase Order pada nomor PO '%s'.") % (self.purchase_request_id.agreement_id.name, mpa_exist_in_po_id.name))
                self.is_rfq = True
                self.is_agreement = True
                self.agreement_id = self.purchase_request_id.agreement_id.id
                self.requestor_id = self.purchase_request_id.agreement_id.bidding_id.requestor_id.id
            
            if self.purchase_request_id.line_ids:
                order_line = [(5, 0, 0)]
                for line_id in self.purchase_request_id.line_ids:
                    rfq_mpa_line = False
                    if bidding_rfq_line_ids:
                        rfq_mpa_line = bidding_rfq_line_ids.filtered(
                            lambda pt: pt.product_tmpl_id.id == line_id.product_tmpl_id.id and pt.is_active)
                    if agreement_line_ids:
                        rfq_mpa_line = agreement_line_ids.filtered(
                            lambda pt: pt.product_tmpl_id.id == line_id.product_tmpl_id.id and pt.is_active)
                    
                    variant = False
                    unit_price = False
                    if rfq_mpa_line and rfq_mpa_line[0]:
                        if bidding_rfq_line_ids:
                            variant = rfq_mpa_line[0].product_variant_id.id 
                            unit_price = rfq_mpa_line[0].price_unit
                        elif agreement_line_ids:
                            variant = rfq_mpa_line[0].product_variant_id.id 
                            unit_price = rfq_mpa_line[0].unit_price

                    print(unit_price, variant, 'skkkkkk111111111')

                    new_line_vals = {
                        'order_id': self.id,
                        'purchase_request_line_id': line_id.id,
                        'name': line_id.product_tmpl_id.name,
                        'product_tmpl_id': line_id.product_tmpl_id.id,
                        'product_id': variant if rfq_mpa_line and rfq_mpa_line[0].product_variant_id else line_id.product_id.id,
                        'product_category_id': line_id.product_category_id.id,
                        'description': line_id.description,
                        'new_currency_id': line_id.new_currency_id.id,
                        'currency_rate': line_id.currency_rate,
                        # 'product_qty': line_id.product_qty,
                        'product_qty': line_id.product_qty,
                        'product_uom': line_id.product_uom_id.id,
                        'is_percentage': line_id.is_percentage,
                        'price_unit': unit_price if rfq_mpa_line and rfq_mpa_line[0] else line_id.price_unit,
                        # 'price_unit': 0 if not rfq_mpa_line and self.purchase_request_id.pr_type == 'manual' else 
                        #             rfq_mpa_line.price_unit if bidding_rfq_line_ids else 
                        #             rfq_mpa_line.unit_price if agreement_line_ids else
                        #             line_id.price_unit,
                        'subtotal_currency': line_id.subtotal_currency,
                        'price_subtotal': line_id.estimated_cost,
                        'latest_delivery_date': line_id.end_date,
                        'is_manajemen_fee': line_id.is_manajemen_fee,
                    }
                    order_line.append((0, 0, new_line_vals))
                self.order_line = order_line

        #     if self.purchase_request_id.pr_type == 'manual' and not self.purchase_request_id.is_dpl:
        #         bidding_id = self.env['bidding.bidding'].search([('is_purchase_request', '=', True),
        #             ('purchase_request_id', '=', self.purchase_request_id.id)])
        #         if len(bidding_id) == 1:
        #             if bidding_id.bidding_type == 'rfq':
        #                 self.is_rfq = True
        #                 bidding_rfq_id = self.env['bidding.rfq'].search([('bidding_id', '=', bidding_id.id),
        #                     # ('purchase_request_id', '=', self.purchase_request_id.id),
        #                     ('state', '=', 'submit')])
        #                 if len(bidding_rfq_id) == 1:
        #                     self.bidding_rfq_id = bidding_rfq_id.id
        #             elif bidding_id.bidding_type == 'rfp':
        #                 self.is_agreement = True
        #                 agreement_id = self.env['bidding.agreement'].search([('bidding_id', '=', bidding_id.id),
        #                     # ('purchase_request_id', '=', self.purchase_request_id.id),
        #                     ('state', '=', 'submit')])
        #                 if len(agreement_id) == 1:
        #                     self.agreement_id = agreement_id.id
            
        #     order_line = [(5, 0, 0)]
        #     if self.purchase_request_id:
        #         if self.purchase_request_id.line_ids:
        #             line_ids = False
        #             if self.purchase_request_id.pr_type == 'manual':
        #                 if self.bidding_rfq_id:
        #                     line_ids = False
        #                 elif self.agreement_id:
        #                     line_ids = False
        #                 elif self.dpl_id:
        #                     line_ids = self.purchase_request_id.line_ids
        #             else:
        #                 line_ids = self.purchase_request_id.line_ids
        #             if line_ids:
        #                 for line in line_ids:
        #                     if self.purchase_request_id.pr_type == 'manual':
        #                         if self.bidding_rfq_id or self.agreement_id:
        #                             matched_product_tmpl_id = self.purchase_request_id.line_ids.filtered(
        #                                 lambda pt: pt.product_tmpl_id.id == line.product_tmpl_id.id and pt.product_id.id == line.product_id.id
        #                             )
        #                             line = matched_product_tmpl_id if matched_product_tmpl_id else False
        #                         # if self.bidding_rfq_id:
        #                         #    matched_product_tmpl_id = self.purchase_request_id.line_ids.filtered(
        #                         #        lambda pt: pt.product_tmpl_id.id == line.product_tmpl_id.id and pt.product_id.id == line.product_id.id
        #                         #    )
        #                         #    line_rfq = matched_product_tmpl_id if matched_product_tmpl_id else False


        #                     if line.product_qty > 0 and line.price_unit > 0:
        #                         new_line_vals = {
        #                             'order_id': self.id,
        #                             'purchase_request_line_id': line.id if 'id' in line else False,
        #                             'name': line.product_tmpl_id.name,
        #                             'product_tmpl_id': line.product_tmpl_id.id,
        #                             'product_id': line.product_id.id,
        #                             'product_category_id': line.product_category_id.id,
        #                             'description': line.description,
        #                             'new_currency_id': line.new_currency_id.id if 'new_currency_id' in line else line.currency_id.id if 'currency_id' in line else False,
        #                             'currency_rate': line.currency_rate,
        #                             'product_qty': line.product_qty,
        #                             'product_uom': line.product_uom_id.id,
        #                             'is_percentage': line.is_percentage,
        #                             'price_unit': line.price_unit,
        #                             'subtotal_currency': line.subtotal_currency,
        #                             'price_subtotal': line.estimated_cost if 'estimated_cost' in line else line.price_subtotal if 'price_subtotal' in line else 0,
        #                             'latest_delivery_date': line.end_date if 'end_date' in line else False,
        #                             'is_manajemen_fee': line.is_manajemen_fee,
        #                         }
        #                         order_line.append((0, 0, new_line_vals))
        #                     else:
        #                         raise UserError(_("Quantity Product dan Price Product pada Purchase Request tidak boleh 0"))
        #         else:
        #             raise UserError(_("Tidak ada product pada Purchase Request"))
        #         self.order_line = order_line
        #     else:
        #         self.order_line = order_line
        # if not self.purchase_request_id:
        #     self.is_rfq = False
        #     self.is_agreement = False
        #     self.is_dpl = False
        #     self.bidding_rfq_id = False
        #     self.agreement_id = False
        #     self.dpl_id
        #     self.requestor_id = False
        #     self.unit_id = False
        #     self.group_id = False
        #     self.rkap_id = False
        #     self.rkap_code = False
        #     self.rkap_type_id = False
        #     self.rkap_category_id = False
        #     self.pr_pic_id = False
        #     self.order_line = [(5, 0, 0)]

    # @api.onchange('requestor_id')
    # def onchange_group_unit(self):
    #     self.unit_id = False
    #     self.group_id = False
    #     department = self.requestor_id.department_id
    #     if department:
    #         if department.department_type == '4_unit':
    #             self.unit_id = department.id
    #             self.group_id = department.parent_id.id if department.parent_id else False
    #         elif department.department_type == '3_group':
    #             self.group_id = department.id

    @api.onchange('is_rfq')
    def onchange_is_rfq(self):
        if not self.is_rfq:
            self.bidding_rfq_id = False
    
    @api.onchange('is_agreement')
    def onchange_is_agreement(self):
        if not self.is_agreement:
            self.agreement_id = False
    
    @api.onchange('bidding_rfq_id','agreement_id')
    def onchange_bidding_rfq_agreement(self):
        self.partner_id = False
        self.partner_contact_id = False
        if self.bidding_rfq_id and not self.agreement_id:
            rfq_exist_in_po_id = self.env['purchase.order'].search([('bidding_rfq_id', '=', self.bidding_rfq_id.id)], limit=1)
            if rfq_exist_in_po_id:
                raise UserError(_("Nomor RFQ '%s' sudah menjadi Purchase Order pada nomor PO '%s'.") % (self.bidding_rfq_id.name, rfq_exist_in_po_id.name))
            self.partner_id = self.bidding_rfq_id.partner_id.id
            if self.bidding_rfq_id.bidding_id.bidding_vendor_ids:
                matched_vendor = self.bidding_rfq_id.bidding_id.bidding_vendor_ids.filtered(
                    lambda v: v.vendor_id.id == self.bidding_rfq_id.partner_id.id)
                self.partner_contact_id = matched_vendor.pic_id.id if matched_vendor and len(matched_vendor) == 1 else False
        elif not self.bidding_rfq_id and self.agreement_id:
            # mpa_exist_in_po_id = self.env['purchase.order'].search([('agreement_id', '=', self.agreement_id.id)], limit=1)
            # if mpa_exist_in_po_id:
            #     raise UserError(_("Nomor MPA '%s' sudah menjadi Purchase Order pada nomor PO '%s'.") % (self.agreement_id.name, mpa_exist_in_po_id.name))
            self.partner_id = self.agreement_id.partner_id.id
            if self.agreement_id.bidding_id.bidding_vendor_ids:
                matched_vendor = self.agreement_id.bidding_id.bidding_vendor_ids.filtered(
                    lambda v: v.vendor_id.id == self.agreement_id.partner_id.id)
                self.partner_contact_id = matched_vendor.pic_id.id if matched_vendor and len(matched_vendor) else False
        
        # if self.purchase_request_id:
        #     order_line = [(5, 0, 0)]
        #     line_ids = [(5, 0, 0)]
        #     if self.purchase_request_id:
        #         if self.purchase_request_id.line_ids:
        #             if self.purchase_request_id.pr_type == 'manual':
        #                 if self.bidding_rfq_id:
        #                     line_ids = self.bidding_rfq_id.order_line
        #                 elif self.agreement_id:
        #                     line_ids = self.agreement_id.line_ids
        #                 elif self.dpl_id:
        #                     line_ids = self.purchase_request_id.line_ids
        #             else:
        #                 line_ids = self.purchase_request_id.line_ids
        #             for line in line_ids:
        #                 if self.purchase_request_id.pr_type == 'manual':
        #                     if self.bidding_rfq_id or self.agreement_id:
        #                         matched_product_tmpl_id = self.purchase_request_id.line_ids.filtered(
        #                             lambda pt: pt.product_tmpl_id.id == line.product_tmpl_id.id and pt.product_id.id == line.product_id.id
        #                         )
        #                         line = matched_product_tmpl_id if matched_product_tmpl_id else False
        #                     # if self.bidding_rfq_id:
        #                     #    matched_product_tmpl_id = self.purchase_request_id.line_ids.filtered(
        #                     #        lambda pt: pt.product_tmpl_id.id == line.product_tmpl_id.id and pt.product_id.id == line.product_id.id
        #                     #    )
        #                     #    line_rfq = matched_product_tmpl_id if matched_product_tmpl_id else False
        #                     if not line:
        #                         raise UserError(_("Product pada PR Manual dan RQF/MPA tidak sama"))

        #                 if line.product_qty > 0 and line.price_unit > 0:
        #                     new_line_vals = {
        #                         'order_id': self.id,
        #                         'purchase_request_line_id': line.id if 'id' in line else False,
        #                         'name': line.product_tmpl_id.name,
        #                         'product_tmpl_id': line.product_tmpl_id.id,
        #                         'product_id': line.product_id.id,
        #                         'product_category_id': line.product_category_id.id,
        #                         'description': line.description,
        #                         'new_currency_id': line.new_currency_id.id if 'new_currency_id' in line else line.currency_id.id if 'currency_id' in line else False,
        #                         'currency_rate': line.currency_rate,
        #                         'product_qty': line.product_qty,
        #                         'product_uom': line.product_uom_id.id,
        #                         'is_percentage': line.is_percentage,
        #                         'price_unit': line.price_unit,
        #                         'subtotal_currency': line.subtotal_currency,
        #                         'price_subtotal': line.estimated_cost if 'estimated_cost' in line else line.price_subtotal if 'price_subtotal' in line else 0,
        #                         'latest_delivery_date': line.end_date if 'end_date' in line else False,
        #                         'is_manajemen_fee': line.is_manajemen_fee,
        #                     }
        #                     order_line.append((0, 0, new_line_vals))
        #                 else:
        #                     raise UserError(_("Quantity Product dan Price Product pada Purchase Request tidak boleh 0"))
        #         else:
        #             raise UserError(_("Tidak ada product pada Purchase Request"))
        #         self.order_line = order_line
        #     else:
        #         self.order_line = order_line
    
    # @api.onchange('agreement_id')
    # def onchange_agreement(self):
    #     if self.agreement_id:
    #         self.partner_id = self.agreement_id.partner_id.id
    #     if not self.agreement_id:
    #         self.partner_id = False

    def button_reassign_buyer(self):
        self.ensure_one()
        view_id = self.env.ref('ap_purchase_order.purchase_order_reassign_buyer_form_view').id

        return {
            'type': 'ir.actions.act_window',
            'name': _('Re-Assign Buyer'),
            'view_mode': 'form',
            'res_model': 'purchase.order.reassign.buyer',
            'target': 'new',
            'views': [[view_id, 'form']],
            'context': {
                'default_purchase_id': self.id,
                # 'default_current_buyer_id': self.buyer_id.id,
                'default_current_buyer_id': self.buyer_id.id if self.buyer_id else False,
            }
        }

    def action_return(self):
        for record in self:
            # state_label = dict(self._fields['state'].selection).get(self.state, '')
            # if record.state != 'pending_approval':
            #     raise UserError(_("Data ini sudah dalam status '%s'. Harap muat ulang halaman untuk melihat status yang diperbarui.") % state_label)
            # record.state = 'return'

            # new_state_label = dict(self._fields['new_state'].selection).get(self.new_state, '')
            # if record.new_state != 'pending_approval':
            #     raise UserError(_("Data ini sudah dalam status '%s'. Harap muat ulang halaman untuk melihat status yang diperbarui.") % new_state_label)
            record.new_state = 'return'

    def action_create_invoice(self):
        res = super(PurchaseOrder, self).action_create_invoice()

        if isinstance(res, dict) and 'res_id' in res:
            move_id = res['res_id']

            if move_id:
                move = self.env['account.move'].browse(move_id)
                if move:
                    move.write({
                        'purchase_order_id': self.id,
                        'vendor_contact_id': self.partner_contact_id.id,
                    })
                else:
                    _logger.error("Account move ID not found: %s", move_id)
        else:
            _logger.error("Response is not a dict or does not contain 'res_id': %s", res)

        return res
    
    def action_open_gr(self):
        # self.ensure_one()
        return {
            'name': _("Good Receipt"),
            'type': 'ir.actions.act_window',
            'res_model': 'stock.picking',
            'view_mode': 'list,form',
            'domain': [('purchase_id', '=', self.id)],
       }
        


    @api.model
    def create(self, vals):
        vals = self._prepare_vals_from_purchase_request(vals)
        record = super(PurchaseOrder, self).create(vals)
        # record._prepare_order_lines_from_pr()
        return record

    def write(self, vals):
        vals = self._prepare_vals_from_purchase_request(vals)
        res = super(PurchaseOrder, self).write(vals)
        if 'purchase_request_id' in vals:
            pass
            # self._prepare_order_lines_from_pr()
        
        if 'state' in vals and vals.get('state', False) == 'cancel':
            self.budget_line_id.planned_amount_currency = self.budget_line_id.planned_amount_currency + self.amount_total

        return res

    def _prepare_vals_from_purchase_request(self, vals):
        if vals.get('purchase_request_id'):
            purchase_request_id = self.env['purchase.request'].browse(vals['purchase_request_id'])
            vals['pr_pic_id'] = purchase_request_id.pic_id.id
            vals['requestor_id'] = purchase_request_id.requestor_id.id
            vals['unit_id'] = purchase_request_id.unit_id.id
            vals['group_id'] = purchase_request_id.hr_group_id.id
            vals['rkap_id'] = purchase_request_id.rkap_id.id
            vals['rkap_code'] = purchase_request_id.rkap_code
            vals['rkap_type_id'] = purchase_request_id.rkap_type_id.id
            vals['rkap_category_id'] = purchase_request_id.rkap_category_id.id
            vals['buyer_id'] = purchase_request_id.buyer_id.id

            if purchase_request_id.pr_type == 'manual' and not purchase_request_id.is_dpl and not purchase_request_id.dpl_id:
                bidding_ids = self.env['bidding.bidding'].search([('is_purchase_request', '=', True),
                    ('purchase_request_id', '=', purchase_request_id.id),
                    ('state', 'not in', ['draft', 'cancel'])])
                
                if bidding_ids and all(bidding_id.bidding_type == 'rfq' for bidding_id in bidding_ids):
                    vals['is_rfq'] = True
                    if len(bidding_ids) == 1:
                        bidding_rfq_ids = self.env['bidding.rfq'].search([('bidding_id', '=', bidding_ids.id),
                            ('state', '=', 'submit')])
                        if len(bidding_rfq_ids) == 1:
                            vals['bidding_rfq_id'] = bidding_rfq_ids.id
                            vals['partner_id'] = bidding_rfq_ids.partner_id.id
                            bidding_rfq_line_ids = bidding_rfq_ids.order_line
                
                if bidding_ids and all(bidding_id.bidding_type == 'rfp' for bidding_id in bidding_ids):
                    vals['is_agreement'] = True
                    if len(bidding_ids) == 1:
                        agreement_ids = self.env['bidding.agreement'].search([('bidding_id', '=', bidding_ids.id),
                            ('state', '=', 'submit')])
                        if len(agreement_ids) == 1:
                            vals['agreement_id'] = agreement_ids.id
                            vals['partner_id'] = agreement_ids.partner_id.id
                            agreement_line_ids = agreement_ids.order_line
            
            if purchase_request_id.pr_type == 'manual' and purchase_request_id.is_dpl and purchase_request_id.dpl_id:
                vals['is_dpl'] = True
                vals['dpl_id'] = purchase_request_id.dpl_id.id

            if purchase_request_id.pr_type == 'rfq':
                vals['is_rfq'] = True
                vals['bidding_rfq_id'] = purchase_request_id.rfq_id.id
                vals['requestor_id'] = purchase_request_id.rfq_id.bidding_id.requestor_id.id

            if purchase_request_id.pr_type == 'rfp':
                vals['is_agreement'] = True
                vals['agreement_id'] = purchase_request_id.agreement_id.id
                vals['requestor_id'] = purchase_request_id.agreement_id.bidding_id.requestor_id.id

        if vals.get('bidding_rfq_id') and not vals.get('agreement_id'):
            bidding_rfq_id = self.env['bidding.rfq'].browse(vals['bidding_rfq_id'])
            vals['partner_id'] = bidding_rfq_id.partner_id.id
            if bidding_rfq_id.bidding_id.bidding_vendor_ids:
                matched_vendor = bidding_rfq_id.bidding_id.bidding_vendor_ids.filtered(
                    lambda v: v.vendor_id.id == vals['partner_id'])
                vals['partner_contact_id'] = matched_vendor.pic_id.id if matched_vendor else False
        elif not vals.get('bidding_rfq_id') and vals.get('agreement_id'):
            agreement_id = self.env['bidding.agreement'].browse(vals['agreement_id'])
            vals['partner_id'] = agreement_id.partner_id.id
            if agreement_id.bidding_id.bidding_vendor_ids:
                matched_vendor = agreement_id.bidding_id.bidding_vendor_ids.filtered(
                    lambda v: v.vendor_id.id == vals['partner_id'])
                vals['partner_contact_id'] = matched_vendor.pic_id.id if matched_vendor else False

        # if vals.get('requestor_id'):
        #     requestor_id = self.env['hr.employee'].browse(vals['requestor_id'])
        #     department = requestor_id.department_id
        #     if department:
        #         if department.department_type == '4_unit':
        #             vals['unit_id'] = department.id
        #             vals['group_id'] = department.parent_id.id if department.parent_id else False
        #         elif department.department_type == '3_group':
        #             vals['group_id'] = department.id

        return vals

    @api.onchange('purchase_request_id', 'agreement_id', 'bidding_rfq_id')
    def _prepare_order_lines_from_pr(self):
        for rec in self:
            print('onceeeeeeeeeeeeeeee')
            if rec.purchase_request_id.line_ids:
                order_line = [(5, 0, 0)]
                for line_id in rec.purchase_request_id.line_ids:
                    rfq_mpa_line = False
                    if rec.bidding_rfq_id:
                        rfq_mpa_line = rec.bidding_rfq_id.order_line.filtered(
                            lambda pt: pt.product_tmpl_id.id == line_id.product_tmpl_id.id and pt.is_active)
                    if rec.agreement_id:
                        rfq_mpa_line = rec.agreement_id.line_ids.filtered(
                            lambda pt: pt.product_tmpl_id.id == line_id.product_tmpl_id.id and pt.is_active)
                    
                    variant = False
                    unit_price = False
                    if rfq_mpa_line and rfq_mpa_line[0]:
                        if rec.bidding_rfq_id:
                            variant = rfq_mpa_line[0].product_variant_id.id 
                            unit_price = rfq_mpa_line[0].price_unit
                        elif rec.agreement_id:
                            variant = rfq_mpa_line[0].product_variant_id.id 
                            unit_price = rfq_mpa_line[0].unit_price
                    new_line_vals = {
                        'order_id': rec.id,
                        'purchase_request_line_id': line_id.id,
                        'name': line_id.product_tmpl_id.name,
                        'product_tmpl_id': line_id.product_tmpl_id.id,
                        'product_id': variant if rfq_mpa_line and rfq_mpa_line[0].product_variant_id else line_id.product_id.id,
                        'product_category_id': line_id.product_category_id.id,
                        'description': line_id.description,
                        'new_currency_id': line_id.new_currency_id.id,
                        'currency_rate': line_id.currency_rate,
                        # 'product_qty': line_id.product_qty,
                        'product_qty': line_id.product_qty,
                        'product_uom': line_id.product_uom_id.id,
                        'is_percentage': line_id.is_percentage,
                        # 'price_unit': line_id.price_unit,
                        'price_unit': unit_price if rfq_mpa_line and rfq_mpa_line[0] else line_id.price_unit,
                        # 'price_unit': 0 if not rfq_mpa_line and rec.purchase_request_id.pr_type == 'manual' else 
                        #             rfq_mpa_line.price_unit if bidding_rfq_line_ids else 
                        #             rfq_mpa_line.unit_price if agreement_line_ids else
                        #             line_id.price_unit,
                        'subtotal_currency': line_id.subtotal_currency,
                        'price_subtotal': line_id.estimated_cost,
                        'latest_delivery_date': line_id.end_date,
                        'is_manajemen_fee': line_id.is_manajemen_fee,
                    }
                    order_line.append((0, 0, new_line_vals))
                rec.order_line = order_line


            # order_line = [(5, 0, 0)]
            # if rec.purchase_request_id:
            #     if rec.purchase_request_id.line_ids:
            #         if rec.purchase_request_id.pr_type == 'manual':
            #             if rec.bidding_rfq_id:
            #                 line_ids = rec.bidding_rfq_id.order_line
            #             elif rec.agreement_id:
            #                 line_ids = rec.agreement_id.line_ids
            #             elif rec.dpl_id:
            #                 line_ids = rec.purchase_request_id.line_ids
            #         else:
            #             line_ids = rec.purchase_request_id.line_ids
            #         for line in line_ids:
            #             if rec.purchase_request_id.pr_type == 'manual':
            #                 if rec.bidding_rfq_id or rec.agreement_id:
            #                     matched_product_tmpl_id = rec.purchase_request_id.line_ids.filtered(
            #                         lambda pt: pt.product_tmpl_id.id == line.product_tmpl_id.id and pt.product_id.id == line.product_id.id
            #                     )
            #                     line = matched_product_tmpl_id if matched_product_tmpl_id else False
            #                 # if rec.bidding_rfq_id:
            #                 #    matched_product_tmpl_id = rec.purchase_request_id.line_ids.filtered(
            #                 #        lambda pt: pt.product_tmpl_id.id == line.product_tmpl_id.id and pt.product_id.id == line.product_id.id
            #                 #    )
            #                 #    line_rfq = matched_product_tmpl_id if matched_product_tmpl_id else False


            #             if line.product_qty > 0 and line.price_unit > 0:
            #                 new_line_vals = {
            #                     'order_id': self.id,
            #                     'purchase_request_line_id': line.id if 'id' in line else False,
            #                     'name': line.product_tmpl_id.name,
            #                     'product_tmpl_id': line.product_tmpl_id.id,
            #                     'product_id': line.product_id.id,
            #                     'product_category_id': line.product_category_id.id,
            #                     'description': line.description,
            #                     'new_currency_id': line.new_currency_id.id if 'new_currency_id' in line else line.currency_id.id if 'currency_id' in line else False,
            #                     'currency_rate': line.currency_rate,
            #                     'product_qty': line.product_qty,
            #                     'product_uom': line.product_uom_id.id,
            #                     'is_percentage': line.is_percentage,
            #                     'price_unit': line.price_unit,
            #                     'subtotal_currency': line.subtotal_currency,
            #                     'price_subtotal': line.estimated_cost if 'estimated_cost' in line else line.price_subtotal if 'price_subtotal' in line else 0,
            #                     'latest_delivery_date': line.end_date if 'end_date' in line else False,
            #                     'is_manajemen_fee': line.is_manajemen_fee,
            #                 }
            #                 order_line.append((0, 0, new_line_vals))
            #             else:
            #                 raise UserError(_("Quantity Product dan Price Product pada Purchase Request tidak boleh 0"))
            #     else:
            #         raise UserError(_("Tidak ada product pada Purchase Request"))
            #     self.order_line = order_line
            # else:
            #     self.order_line = order_line

            # order_line = [(5, 0, 0)]
            # if rec.purchase_request_id:
            #     if rec.purchase_request_id.line_ids:
            #         for line in rec.purchase_request_id.line_ids:
            #             if line.product_qty > 0 and line.price_unit > 0:
            #                 new_line_vals = {
            #                     'order_id': rec.id,
            #                     'purchase_request_line_id': line.id,
            #                     'name': line.product_tmpl_id.name,
            #                     'product_tmpl_id': line.product_tmpl_id.id,
            #                     'product_id': line.product_id.id,
            #                     'product_category_id': line.product_category_id.id,
            #                     'description': line.name,
            #                     'new_currency_id': line.new_currency_id.id,
            #                     'currency_rate': line.currency_rate,
            #                     'product_qty': line.product_qty,
            #                     'product_uom': line.product_uom_id.id,
            #                     'is_percentage': line.is_percentage,
            #                     'price_unit': line.price_unit,
            #                     'subtotal_currency': line.subtotal_currency,
            #                     'price_subtotal': line.estimated_cost,
            #                     'latest_delivery_date': line.end_date,
            #                     'is_manajemen_fee': line.is_manajemen_fee,
            #                 }
            #                 order_line.append((0, 0, new_line_vals))
            #             else:
            #                 raise UserError(_("Quantity Product dan Price Product pada Purchase Request tidak boleh 0"))
            #         rec.order_line = order_line
            #     else:
            #         raise UserError(_("Tidak ada product pada Purchase Request"))



