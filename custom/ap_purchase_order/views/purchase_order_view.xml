<odoo>

    <record id="inherit_purchase_order_form" model="ir.ui.view">
        <field name="name">inherit.purchase.order.form</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='payment_term_id']" position="attributes">
                <attribute name="required">1</attribute>
            </xpath>

            <xpath expr="//div[@class='oe_title']/span[contains(text(), 'Request for Quotation ')]" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//div[@class='oe_title']/span[contains(text(), 'Purchase Order ')]" position="attributes">
                <attribute name="invisible">0</attribute>
            </xpath>

            <xpath expr="(//button[@name='print_quotation'])[1]" position="attributes">
                <attribute name="string">Print PO</attribute>
                <attribute name="invisible">new_state != 'open'</attribute>
            </xpath>

            <xpath expr="(//button[@name='print_quotation'])[2]" position="attributes">
                <attribute name="string">Print PO</attribute>
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="(//button[@name='action_create_invoice'])[1]" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="(//button[@name='action_create_invoice'])[2]" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="(//button[@name='button_confirm'])[1]" position="attributes">
                <attribute name="string">Approve</attribute>
            </xpath>
            <xpath expr="(//button[@name='button_confirm'])[1]" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="(//button[@name='button_confirm'])[2]" position="attributes">
                <attribute name="string">Acknowledge</attribute>
                <attribute name="groups">ap_purchase_order.group_purchase_order_procurement_support_associate</attribute>
            </xpath>
            <xpath expr="(//button[@name='button_confirm'])[2]" position="attributes">
                <attribute name="invisible">new_state not in ['pending_acknowledge']</attribute>
            </xpath>

            <xpath expr="(//button[@name='action_rfq_send'])[1]" position="attributes">
                <attribute name="invisible">new_state != 'open'</attribute>
            </xpath>

            <xpath expr="(//button[@name='action_rfq_send'])[2]" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="(//button[@name='action_rfq_send'])[3]" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//button[@name='action_bill_matching']" position="attributes">
                <attribute name="invisible">new_state != 'open'</attribute>
            </xpath>

            <xpath expr="//button[@name='action_view_invoice']" position="after">
                <button class="oe_stat_button" name="action_open_gr" type="object" icon="fa-bars" invisible="new_state not in ['open','done','closed']">
                    <div class="o_stat_info">
                        <!-- <field name="gr_count" widget="statinfo" string="Good Receiptxx"/> -->
                        <span class="o_stat_text">
                            Good Receipt
                        </span>
                    </div>
                </button>
            </xpath>

            <xpath expr="//button[@name='button_confirm']" position="after">
                <button string="Reject" name="action_reject" type="object" class="oe_highlight" invisible="new_state not in ('pending_approval', 'pending_acknowledge')"/>
                <button name="action_return" string="Return" type="object" invisible="new_state != 'pending_approval'"/>
                <button name="button_reassign_buyer" string="Re-assign Buyer" type="object" invisible="new_state in ('draft')"/>
                <button name="action_change_order" string="Change Order" type="object" class="btn-primary" invisible="new_state != 'open' or gr_count > 0"/>
                <button name="action_approve_change_order" string="Approve" type="object" class="btn-primary" invisible="1"/>
                <button name="action_set_to_draft" string="Set to Draft" type="object" invisible="new_state not in ('open','closed', 'pending_approval')"/>
                <field name="gr_count" invisible="1"/>
            </xpath>

            <!-- <xpath expr="//button[@name='button_draft']" position="attributes">
                <attribute name="string">Reset</attribute>
                <attribute name="invisible">new_state != 'pending_acknowledge'</attribute>
            </xpath> -->

            <xpath expr="//field[@name='partner_id']" position="after">
                <!-- <field name="partner_contact_id" required="1" readonly="is_dpl != True or new_state != 'draft' or is_change_po == True" domain="[('parent_id', '=', partner_id), ('parent_id', '!=', False)]"/> -->
                <field name="partner_contact_id" required="1" readonly="new_state not in ('draft','return') or is_change_po == True" domain="[('parent_id', '=', partner_id), ('parent_id', '!=', False)]"/>
                <field name="email_contact" force_save="1" readonly="1"/>
                <span class="o_form_label o_td_label o_address_type" name="address_name">
                    <span>Address</span>
                </span>

                <div class="o_address_format">
                    <field name="street" placeholder="Street..." class="o_address_street" force_save="1" readonly="1"/>
                    <field name="street2" placeholder="Street 2..." class="o_address_street" force_save="1" readonly="1"/>
                    <field name="city" placeholder="City" class="o_address_city" force_save="1" readonly="1"/>
                    <field name="state_id" class="o_address_state" placeholder="State" options="{'no_open': True, 'no_quick_create': True}" force_save="1" readonly="1"/>
                    <field name="zip" placeholder="ZIP" class="o_address_zip" force_save="1" readonly="1"/>
                    <div name="partner_address_country" class="d-flex justify-content-between">
                        <field name="country_id" placeholder="Country" class="o_address_country" options="{&quot;no_open&quot;: True, &quot;no_create&quot;: True}" force_save="1" readonly="1"/>
                    </div>
                </div>
            </xpath>

            <xpath expr="//field[@name='partner_ref']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='requisition_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='currency_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='date_order']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='date_order']" position="after">
                <field name="transaction_date" string="Transaction Date" readonly="new_state not in ('draft','return')"/>
            </xpath>

            <xpath expr="//div[@name='date_approve']" position="after">
                <field name="date_purchase" readonly="1"/>
                <field name="latest_delivery_date" readonly="1"/>
                <field name="validity_period" readonly="1" force_save='1' />
                <field name="is_agreement" invisible="is_rfq == True" readonly="is_agreement_readonly == True"/>
                <!-- <field name="agreement_id" invisible="is_agreement != True" required="is_agreement == True" readonly="agreement_id_readonly == True" domain="[('state', '=', 'submit'), ('bidding_id.purchase_request_id', '=', purchase_request_id)]"/> ,('purchase_request_id', '=', purchase_request_id) -->
                <field name="agreement_id" invisible="is_agreement != True" required="is_agreement == True" readonly="agreement_id_readonly == True"/>
                <field name="rkap_id" required="1" readonly="1"/>
                <field name="budget_line_id" invisible="1"/>
                <field name="rkap_code" readonly="1"/>
                <field name="rkap_category_id" readonly="1"/>
                <field name="rkap_type_id" readonly="1"/>
                <field name="employee_id" invisible="1"/>
                <field name="accrue_expense" readonly="new_state not in ('draft','return')" invisible="1"/>
                <!-- <field name="project" readonly="new_state not in ('draft','return')"/> -->
                <field name="buyer_id" readonly="new_state not in ('draft','return')" required="1"/>
                <field name="supplier_reacknowledge" readonly="new_state != 'draft'" invisible=" is_change_po == False"/>
                <field name="is_change_po" invisible="1"/>
                
            </xpath>

             <xpath expr="//label[@for='date_planned']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//div[@name='date_planned_div']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='date_planned']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

             <xpath expr="//label[@for='date_approve']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='date_approve']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//div[@name='reminder']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//label[@for='receipt_reminder_email']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='picking_type_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='order_line']/list" position="attributes">
                <attribute name="delete">0</attribute>
                <attribute name="create">0</attribute>
            </xpath>

            <xpath expr="//field[@name='order_line']/list//field[@name='name']" position="attributes">
                 <attribute name="column_invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='order_line']/list//field[@name='date_planned']" position="attributes">
                 <attribute name="column_invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='order_line']/list//field[@name='propagate_cancel']" position="attributes">
                 <attribute name="column_invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='order_line']/list//field[@name='analytic_distribution']" position="attributes">
                 <attribute name="column_invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='order_line']/list//field[@name='discount']" position="attributes">
                 <attribute name="column_invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='order_line']/list//button[@name='action_purchase_history']" position="attributes">
                 <attribute name="column_invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='order_line']/list//field[@name='product_uom']" position="attributes">
                 <attribute name="optional">hide</attribute>
            </xpath>

            <xpath expr="//field[@name='order_line']/list//field[@name='product_uom'][2]" position="attributes">
                 <attribute name="readonly">1</attribute>
                 <attribute name="force_save">1</attribute>
            </xpath>

            <xpath expr="//field[@name='order_line']/list//field[@name='product_id']" position="before">
                <field name="product_tmpl_id" required="1" readonly="1" force_save="1" />
            </xpath>

            <xpath expr="//field[@name='order_line']/list//field[@name='product_id']" position="after">
                <field name="product_category_id" readonly="1" force_save="1" width="150px"/>
                <field name="description" string="Description" readonly="1" force_save="1" />
                <field name="domain_currency" column_invisible="1" />
                <field name="new_currency_id" domain="domain_currency" readonly="1" force_save="1"/>
                <field name="is_company_currency" column_invisible="1"/>
                <field name="currency_rate" invisible="is_company_currency == True" readonly="1" force_save="1"/>
            </xpath>

            <xpath expr="//field[@name='order_line']/list//field[@name='price_unit']" position="before">
                <field name="is_percentage" readonly="1" force_save="1" width="100px"/>
            </xpath>

            <xpath expr="//field[@name='order_line']/list//field[@name='price_subtotal']" position="after">
                <field name="latest_delivery_date" readonly="1" force_save="1" width="150px"/>
                <field name="is_manajemen_fee" invisible="is_percentage == True" width="150px"/>
            </xpath>

            <xpath expr="//field[@name='order_line']/list//field[@name='price_unit']" position="after">
                <field name="subtotal_currency" width="150px"/>
            </xpath>

            <xpath expr="//field[@name='order_line']/list//field[@name='product_id']" position="attributes">
                <attribute name="domain">[('product_tmpl_id', '=', product_tmpl_id), ('is_product_variant', '=', True)]</attribute>
                <attribute name="string">Product Variant</attribute>
                <attribute name="readonly">1</attribute>
                <attribute name="force_save">1</attribute>
            </xpath>

            <xpath expr="//field[@name='order_line']/list//field[@name='taxes_id']" position="attributes">
                <attribute name="column_invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='order_line']/list//field[@name='price_subtotal']" position="after">
                <field name="price_total" column_invisible="1"/>
            </xpath>

            <xpath expr="//field[@name='order_line']/list//field[@name='price_subtotal']" position="attributes">
                <attribute name="string">Subtotal IDR</attribute>
            </xpath>

            <xpath expr="//field[@name='order_line']/list//field[@name='product_qty']" position="attributes">
                <attribute name="required">0</attribute>
                <attribute name="readonly">parent.is_change_po != True</attribute>
                <attribute name="force_save">1</attribute>
            </xpath>

            <xpath expr="//field[@name='order_line']/list//field[@name='price_unit']" position="attributes">
                <attribute name="required">0</attribute>
                <attribute name="readonly">unit_price_source != 0</attribute>
                <attribute name="force_save">1</attribute>
            </xpath>

            <xpath expr="//field[@name='payment_term_id']" position="before">
                <field name="receipt_state" readonly="new_state not in ('draft','return')" invisible="1"/>
                <field name="bill_state" readonly="new_state not in ('draft','return')" invisible="1"/>
            </xpath>

            <xpath expr="//field[@name='payment_term_id']" position="after">
                <field name="change_order_versi" readonly="1"/>
            </xpath>

            <xpath expr="//field[@name='fiscal_position_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='user_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='origin']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='payment_term_id']" position="attributes">
                <attribute name="readonly">new_state not in ('draft','return')</attribute>
            </xpath>

            <!-- <xpath expr="//field[@name='partner_id']" position="attributes">
                <attribute name="readonly">is_dpl != True or new_state != 'draft' or is_change_po == True</attribute>
            </xpath> -->

            <xpath expr="//field[@name='partner_id']" position="attributes">
                <attribute name="readonly">new_state not in ('draft','return')</attribute>
                <attribute name="domain">[('supplier_rank', '!=', 0),('parent_id','=',False)]</attribute>
            </xpath>

            <xpath expr="//field[@name='order_line']" position="attributes">
                <attribute name="readonly">new_state not in ('draft','return')</attribute>
            </xpath>

            <xpath expr="//page[@name='purchase_delivery_invoice']" position="after">
                <page string="Attachments">
                    <field name="order_attachment_ids">
                        <list editable="bottom">
                            <field name="attachment" widget="binary" filename="filename"/>
                            <field name="filename" column_invisible="1"/>
                            <field name="document_name" required="1"/>
                            <field name="document_type" required="1"/>
                            <field name="company_id" readonly="1" column_invisible="1"/>
                        </list>
                        <form string="Attachments">
                            <group>
                                <group>
                                    <field name="attachment" widget="binary" filename="filename"/>
                                    <field name="filename" invisible="1"/>
                                    <field name="document_name" required="1"/>
                                </group>
                                <group>
                                    <field name="document_type" required="1"/>
                                    <field name="company_id" readonly="1" invisible="1"/>
                                </group>
                            </group>
                        </form>
                    </field>
                </page>
            </xpath>

            <xpath expr="//field[@name='tax_totals']" position="replace">
                <field name="tax_totals" invisible="1"/>
                <field name="amount_total" />
            </xpath>

        </field>
    </record>

    <record id="purchase_order_stock_view_form_inherit" model="ir.ui.view">
        <field name="name">inherit.purchase.order.stock.form</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase_stock.purchase_order_view_form_inherit"/>
        <field name="arch" type="xml">

            <xpath expr="//field[@name='order_line']/list//button[@name='action_product_forecast_report']" position="attributes">
                 <attribute name="column_invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='incoterm_id']" position="attributes">
                <attribute name="string">Delivery Term</attribute>
            </xpath>

            <xpath expr="//button[@name='confirm_reminder_mail']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='incoterm_location']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='incoterm_id']" position="attributes">
                <attribute name="readonly">new_state not in ('draft','return')</attribute>
            </xpath>

            <xpath expr="//field[@name='incoterm_id']" position="before">
                <field name="delivery_location_id" readonly="new_state not in ('draft','return')"/>
            </xpath>

            <xpath expr="//field[@name='incoterm_id']" position="after">
                <field name="company_id" readonly="new_state not in ('draft','return')"/>
                <field name="purchase_assign_id" readonly="new_state not in ('draft','return')"/>
            </xpath>

        </field>
    </record>

    <record id="inherit_purchase_order_desc_form" model="ir.ui.view">
        <field name="name">inherit.purchase.order.desc.form</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase_fpip.inherit_purchase_order_form_view_id_inherit_module_purchase"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='description']" position="attributes">
                <attribute name="string">PO Description</attribute>
            </xpath>

            <xpath expr="//field[@name='description']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='partner_id']" position="before">
                <field name="purchase_request_id" readonly="new_state not in ('draft','return')  or is_change_po == True" domain="[('state', '=', 'approved')]" required="1" />
                <field name="description" string="PO Description" readonly="new_state not in ('draft','return')"/>
                <field name="is_dpl" invisible="is_agreement == True" readonly="is_dpl_readonly == True"/>
                <field name="dpl_id" invisible="is_dpl != True" required="is_dpl == True" readonly="is_dpl == True" domain="[('state', '=', 'done')]"/>
            </xpath>

            <xpath expr="//field[@name='created_user_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='requester_user_id']" position="attributes">
                <attribute name="invisible">1</attribute>
                <!-- <attribute name="readonly">new_state not in ('draft','return')</attribute> -->
            </xpath>

            <xpath expr="//field[@name='requester_user_id']" position="before">
                <field name="is_rfq" invisible="is_agreement == True or pr_type == 'rfq'" readonly="is_bidding_rfq_readonly == True"/>
                <!-- <field name="rfq_id" invisible="is_rfq != True" required="is_rfq == True" readonly="new_state not in ('draft','return')"/> -->
                <!-- <field name="bidding_rfq_id" domain="[('state', '=', 'submit'), ('bidding_id.purchase_request_id', '=', purchase_request_id)]" invisible="is_rfq != True or is_agreement" required="is_rfq == True and not is_agreement" readonly="pr_type == 'rfq' or new_state != 'draft'" /> -->
                <field name="bidding_rfq_id" domain="[('partner_id','=',partner_id), ('state', '=', 'submit')]" invisible="is_rfq != True or is_agreement" required="is_rfq == True and not is_agreement" readonly="pr_type == 'rfq' or new_state != 'draft'" />
                <field name="pr_pic_id" readonly="1"/>
                <field name="requestor_id" required="1" readonly="new_state not in ('draft','return')"/>
            </xpath>

             <xpath expr="//field[@name='requester_user_id']" position="after">
                <field name="unit_id" readonly="1"/>
                <field name="group_id" readonly="1"/>
                <field name="is_po_acknowledge" readonly="new_state not in ('draft','return')" invisible="1"/>
            </xpath>

        </field>
    </record>

    <record id="inherit_purchase_order_tree_view_id_inherit_module_purchase" model="ir.ui.view">
        <field name="name">purchase.order.view.form.inherit</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='state']" position="replace">
                <field name="new_state" string="status" />
            </xpath>
        </field>
    </record>

</odoo>