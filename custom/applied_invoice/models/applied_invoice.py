from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError


class MiscellaneousMiscellaneous(models.Model):
    _inherit = 'miscellaneous.miscellaneous'

    ### Add Period ###
    period_id = fields.Many2one(
        "invoicing.period.line", string="Period", compute="_compute_get_period", store=True
    )

    ### Add Period #
    @api.depends("date")
    def _compute_get_period(self):
        if self:
            for rec in self:
                rec.period_id = False
                if rec.date:
                    period = (
                        self.env["invoicing.period.line"]
                        .sudo()
                        .search(
                            [
                                ("date_start", "<=", rec.date),
                                ("date_end", ">=", rec.date),
                                ('move_type', '=', 'out_invoice'),                                
                            ],
                            limit=1,
                        )
                    )
                    if period:
                        rec.period_id = period.id

    @api.model_create_multi
    def create(self, vals_list):
        # OVERRIDE
        write_off_line_vals_list = []

        for val in vals_list:
            # Sequence assignment based on type
            if val.get('misc_name', 'New') == 'New':
                if val.get('misc_type') == 'receive':
                    val['misc_name'] = self.env['ir.sequence'].next_by_code('miscellaneous.receipt') or '/'
                elif val.get('misc_type') == 'payment':
                    val['misc_name'] = self.env['ir.sequence'].next_by_code('miscellaneous.payment') or '/'

            # Journal and currency defaults
            if 'journal_id' not in val:
                val['journal_id'] = self._get_default_journal().id

            if 'currency_id' not in val:
                journal = self.env['account.journal'].browse(val['journal_id'])
                val['currency_id'] = journal.currency_id.id or journal.company_id.currency_id.id

            # Validate amount
            if float(val.get('amount', 0)) <= 0:
                raise UserError(_("The Amount Misc must be greater than 0"))

            # Store write-off info (if any) separately
            write_off_line_vals_list.append(val.pop('write_off_line_vals', None))

            # Force correct move_type
            val['move_type'] = 'entry'
            val['name'] = '/'

            # Validate period
            date = val.get('date')
            if date:
                period = (
                    self.env["invoicing.period.line"]
                    .sudo()
                    .search(
                        [
                            ("date_start", "<=", date),
                            ("date_end", ">=", date),
                            ("move_type", "=", "out_invoice"),
                        ],
                        limit=1,
                    )
                )
                if period and period.state == 'close':
                    raise UserError(_("You cannot select a date from a closed invoice period."))

        # Create the records
        records = super().create(vals_list)

        # No move lines written here — will be handled later in action_post
        # Optionally, you can validate or prepare other related data here

        return records


    def write(self, vals):
        # OVERRIDE

        # Validate if 'date' is being updated
        if 'date' in vals:
            new_date = vals['date']
            if new_date:
                for rec in self:
                    period = (
                        self.env["invoicing.period.line"]
                        .sudo()
                        .search(
                            [
                                ("date_start", "<=", new_date),
                                ("date_end", ">=", new_date),
                                ("move_type", "=", "out_invoice"),
                            ],
                            limit=1,
                        )
                    )
                    if period and period.state == "close":
                        raise UserError(_("You cannot select a date from a closed invoice period (Record ID: %s).") % rec.id)

        # Track move states
        original_move_states = {rec.id: rec.move_id.state for rec in self}

        # Only bring moves back to draft if really necessary
        for rec in self:
            if rec.move_id and rec.move_id.state == 'posted':
                if set(vals.keys()) & {'date', 'amount', 'journal_id'}:
                    rec.move_id.button_draft()

        # Write main record
        res = super(MiscellaneousMiscellaneous, self.with_context(skip_readonly_check=True)).write(vals)

        for rec in self:
            rec._synchronize_to_moves(set(vals.keys()))

            if not rec.analytic_account_id:
                rec.analytic_account_id = rec._get_default_analytic_account()

            if rec.applied_customer_move_id:
                rec.applied_customer_move_id.with_context(skip_readonly_check=True).write({
                    'date': rec.applied_partner_date
                })

            if rec.reserve_amount < 0:
                raise UserError(_("Available amount cannot be negative (Record ID: %s)") % rec.id)

        # Restore move state if it was not changed
        for rec in self:
            original_state = original_move_states.get(rec.id)
            if rec.move_id and rec.move_id.state != original_state:
                if original_state == 'posted':
                    rec.move_id._post(soft=False)  # Re-post only if it was originally posted

        return res


    def _synchronize_to_moves(self, changed_fields):
        if self._context.get('skip_account_move_synchronization'):
            return

        if not any(field_name in changed_fields for field_name in (
                'date', 'amount', 'receipt_type_id', 'currency_id', 'partner_id', 'destination_account_id',
                'partner_bank_id', 'journal_id', 'description', 'journal_group',
        )):
            return

        for pay in self.with_context(skip_account_move_synchronization=True):
            liquidity_lines, counterpart_lines, writeoff_lines = pay._seek_for_lines()

            # Make sure to preserve the write-off amount.
            # This allows to create a new payment with custom 'line_ids'.

            if liquidity_lines and counterpart_lines and writeoff_lines:

                counterpart_amount = sum(counterpart_lines.mapped('amount_currency'))
                writeoff_amount = sum(writeoff_lines.mapped('amount_currency'))

                # To be consistent with the payment_difference made in account.payment.register,
                # 'writeoff_amount' needs to be signed regarding the 'amount' field before the write.
                # Since the write is already done at this point, we need to base the computation on accounting values.
                if (counterpart_amount > 0.0) == (writeoff_amount > 0.0):
                    sign = -1
                else:
                    sign = 1
                writeoff_amount = abs(writeoff_amount) * sign

                write_off_line_vals = {
                    'name': writeoff_lines[0].name,
                    'amount': writeoff_amount,
                    'account_id': writeoff_lines[0].account_id.id,
                }
            else:
                write_off_line_vals = {}

            line_vals_list = pay._prepare_move_line_default_vals(write_off_line_vals=write_off_line_vals)

            line_ids_commands = []
            if liquidity_lines:
                for line in liquidity_lines:
                    line_ids_commands.append((1, line.id, line_vals_list[0]))  # Adjust if needed per-line
            else:
                line_ids_commands.append((0, 0, line_vals_list[0]))

            if counterpart_lines:
                for line in counterpart_lines:
                    line_ids_commands.append((1, line.id, line_vals_list[1]))  # Adjust if needed per-line
            else:
                line_ids_commands.append((0, 0, line_vals_list[1]))

            for line in writeoff_lines:
                line_ids_commands.append((2, line.id))

            for extra_line_vals in line_vals_list[2:]:
                line_ids_commands.append((0, 0, extra_line_vals))

            # Update the existing journal items.
            # If dealing with multiple write-off lines, they are dropped and a new one is generated.

            pay.move_id.with_context(skip_readonly_check=True).write({
                'partner_id': pay.partner_id.id,
                'currency_id': pay.currency_id.id,
                'partner_bank_id': pay.partner_bank_id.id,
                # 'analytic_account_id': pay.analytic_account_id.id,
                'line_ids': line_ids_commands,
            })
            
    def action_post(self):
        """Post the journal entries from draft to posted safely."""

        self.ensure_one()

        # 1. Period validation
        if self.date:
            period = (
                self.env["invoicing.period.line"]
                .sudo()
                .search(
                    [
                        ("date_start", "<=", self.date),
                        ("date_end", ">=", self.date),
                        ("move_type", "=", "out_invoice"),
                    ],
                    limit=1,
                )
            )
            if period and period.state == "close":
                raise UserError(_("You cannot post this record because the date falls in a closed invoice period."))

        # 2. Validation: amount
        if self.reserve_amount < 0:
            raise ValidationError(_("Available Amount cannot be negative"))

        # 3. Ensure move lines exist for the main journal entry
        if self.move_id:
            if not self.move_id.line_ids:
                # Generate journal lines if not yet created
                line_vals = self._prepare_move_line_default_vals()
                self.move_id.write({
                    'line_ids': [(0, 0, line) for line in line_vals]
                })

            # Post the move if in draft
            if self.move_id.state == 'draft':
                self.move_id.with_context(from_misc=True)._post(soft=False)

        # 4. Applied Partner Move Handling
        if self.misc_type == 'receive' and self.misc_partner_id and self.applied_customer_journal_id:
            if not self.applied_customer_move_id:
                if self.journal_group == 'split':
                    self.action_applied_to_partner()
            else:
                move = self.applied_customer_move_id

                # Update line amounts if different
                if move.state == 'draft':
                    if move.amount_total != self.amount:
                        new_lines = []
                        for line in move.line_ids:
                            new_lines.append((1, line.id, {
                                'debit': self.amount if line.debit > 0.0 else 0.0,
                                'credit': self.amount if line.credit > 0.0 else 0.0,
                            }))
                        move.with_context(check_move_validity=False).write({'line_ids': new_lines})

                    # Ensure it's posted
                    move.with_context(from_misc=True)._post(soft=False)

    def action_applied_invoice_bulky(self):
        for rec in self:
            # Check closed period
            if rec.date:
                period = (
                    self.env["invoicing.period.line"]
                    .sudo()
                    .search(
                        [
                            ("date_start", "<=", rec.date),
                            ("date_end", ">=", rec.date),
                            ("move_type", "=", "out_invoice"),
                        ],
                        limit=1,
                    )
                )
                if period and period.state == "close":
                    raise UserError(_("You cannot apply this record because the date falls in a closed invoice period."))

            # Validation for split journal
            if not rec.applied_customer_move_id and rec.journal_group == 'split':
                raise UserError(_("Cannot apply to invoice without applying to customer in 'Split Journal' type. Please apply to customer first."))

            # Proceed with applying invoices
            if (rec.applied_customer_move_id and rec.journal_group == 'split') or \
               (not rec.applied_customer_move_id and rec.journal_group == 'merge'):

                for line in rec.invoice_ids:
                    if line.state == 'draft':
                        if line.applied_amount <= 0:
                            raise ValidationError(_('Applied amount must be more than 0'))

                        # Apply the invoice line
                        line.action_applied_invoice()

                        # Get the actual invoice
                        invoice = line.invoice_id
                        if invoice:
                            # Trigger any flag
                            invoice.force_in_payment_flag = True

                            # Ensure reconciliation is complete
                            lines_to_reconcile = invoice.line_ids.filtered(
                                lambda l: l.account_id.internal_group in ('receivable', 'payable') and not l.reconciled
                            )
                            if lines_to_reconcile:
                                lines_to_reconcile.reconcile()

                            # Flush to force write of residuals
                            invoice.flush_model(['amount_residual', 'amount_residual_signed'])

                            # Explicitly refresh computed fields
                            invoice._compute_amount()
                            invoice._compute_payments_widget_reconciled_info()
                            invoice._get_applied()
                            invoice._compute_payment_state()

                            # Flush payment state again
                            invoice.flush_model(['payment_state'])

    @api.depends(
        'amount',
        'invoice_ids.applied_amount',
        'invoice_ids.transaction_type',
        'invoice_ids.state',
        'invoice_ids.payment_difference_handling'
    )
    def _compute_reserve_amount(self):
        for rec in self:
            if any(line.payment_difference_handling == 'reconcile' for line in rec.invoice_ids):
                rec.reserve_amount = 0.0
                rec.applied_amount = sum(
                    line.applied_amount for line in rec.invoice_ids
                    if line.transaction_type == 'apply' and line.state == 'draft'
                )
                rec.remaining_amount = rec.amount - rec.applied_amount
                continue

            total_reserve_amount = 0.0
            total_amount_applied = 0.0

            for line in rec.invoice_ids:
                if line.transaction_type == 'unapply' and line.state == 'draft':
                    total_reserve_amount += line.applied_amount

                reverse = self.env['account.move'].search([
                    ('reversed_entry_id', '=', line.move_id.id)
                ])
                if line.move_id.state == 'posted' and line.transaction_type == 'apply' and not reverse:
                    total_amount_applied += line.applied_amount

            rec.applied_amount = total_amount_applied
            rec.remaining_amount = rec.amount - total_amount_applied
            rec.reserve_amount = rec.amount - total_reserve_amount - total_amount_applied

class AppliedInvoices(models.Model):
    _inherit = 'applied.invoices'

    amount = fields.Monetary(
        currency_field='misc_currency_id',
        related='misc_id.amount',
        string="Amount",
        readonly=True, default=0)
    invoice_amount = fields.Monetary(
        currency_field='invoice_currency_id',
        compute='_compute_invoice_amount',
        store=True,
        string="Amount Due")

    @api.depends('invoice_id.amount_residual')
    def _compute_invoice_amount(self):
        for rec in self:
            rec.invoice_amount = rec.invoice_id.amount_residual
       
    @api.onchange('applied_amount')
    def compute_payment_difference(self):
        pass

    @api.onchange('applied_amount', 'misc_id.amount', 'payment_difference_handling')
    def _onchange_difference_handling(self):
        if not self.invoice_id or not self.misc_id:
            return

        received_payment = self.misc_id.amount or 0.0

        if self.misc_currency_id == self.invoice_currency_id:
            invoice_amount = self.invoice_amount
        elif self.misc_currency_id == self.company_id.currency_id == self.invoice_currency_id:
            invoice_amount = self.invoice_amount
        else:
            invoice_amount = self.company_id.currency_id._convert(
                self.invoice_amount,
                self.invoice_currency_id,
                self.company_id,
                self.transaction_date
            )

        if self.payment_difference_handling in ['open', 'full_invoice']:
            self.payment_difference = 0
            return

        if self.payment_difference_handling == 'reconcile':
            if not self.applied_amount:
                self.applied_amount = self.invoice_amount_residual

            if self.applied_amount > received_payment:
                self.applied_amount = received_payment

            self.payment_difference = self.applied_amount - received_payment

    @api.depends('applied_amount', 'invoice_amount_residual', 'payment_difference', 'payment_difference_handling')
    def _compute_amount_remaining(self):
        for rec in self:
            if rec.payment_difference_handling == 'reconcile':
                rec.amount_remaining = 0.0
            else:
                if rec.move_id and rec.state != 'posted':
                    rec.invoice_amount_residual = rec.invoice_amount
                    rec.amount_remaining = rec.invoice_amount_residual - rec.applied_amount - rec.payment_difference
                elif rec.state == 'posted':
                    rec.amount_remaining = rec.invoice_amount_residual - rec.applied_amount - rec.payment_difference

    @api.constrains('payment_difference_handling', 'amount', 'invoice_amount_residual')
    def _check_full_invoice_amount(self):
        if self.payment_difference_handling == 'full_invoice' and self.amount < self.invoice_amount_residual:
            raise ValidationError(
                _("Amount must not be less than residual invoice amount when using 'Full Invoice'.")
            )
            
    def _prepare_move_line_default_vals(self, write_off_line_vals=None):
        self.ensure_one()
        write_off_line_vals = write_off_line_vals or {}

        currency = self.misc_currency_id
        company = self.company_id
        partner = self.partner_id
        invoice = self.invoice_id
        misc = self.misc_id

        applied_amount_currency = self.applied_amount
        payment_difference_currency = self.payment_difference

        total_paid_currency = misc.amount or 0.0
        total_paid_balance = currency._convert(total_paid_currency, company.currency_id, company, self.date)

        applied_balance = currency._convert(applied_amount_currency, company.currency_id, company, self.date)
        difference_balance = currency._convert(payment_difference_currency, company.currency_id, company, self.date)

        if self.misc_type == 'receive':
            liquidity_account = misc.applied_partner_account.id
            invoice_account = invoice.partner_id.property_account_receivable_id.id
        elif self.misc_type == 'payment':
            liquidity_account = invoice.partner_id.property_account_payable_id.id
            invoice_account = misc.applied_partner_account.id
        else:
            raise UserError("Invalid misc_type. Must be 'receive' or 'payment'.")

        move_lines = []

        move_lines.append({
            'name': f'Payment on {invoice.name or ""}',
            'date_maturity': self.date,
            'currency_id': currency.id,
            'amount_currency': total_paid_currency,
            'debit': total_paid_balance if total_paid_balance > 0 else 0.0,
            'credit': -total_paid_balance if total_paid_balance < 0 else 0.0,
            'partner_id': partner.id,
            'account_id': liquidity_account,
        })

        if payment_difference_currency and self.payment_difference_handling == 'reconcile':
            move_lines.append({
                'name': f'Write-off for {invoice.name or ""}',
                'currency_id': currency.id,
                'amount_currency': payment_difference_currency,
                'debit': difference_balance if difference_balance > 0 else 0.0,
                'credit': -difference_balance if difference_balance < 0 else 0.0,
                'partner_id': partner.id,
                'account_id': self.writeoff_account_id.id,
            })

        move_lines.append({
            'name': f'Applied to {invoice.name or ""}',
            'date_maturity': self.transaction_date,
            'currency_id': currency.id,
            'amount_currency': -applied_amount_currency,
            'debit': -applied_balance if applied_balance < 0 else 0.0,
            'credit': applied_balance if applied_balance > 0 else 0.0,
            'partner_id': partner.id,
            'account_id': invoice_account,
        })

        return move_lines

class AccountMoveInherit(models.Model):
    _inherit = 'account.move'
    
    force_in_payment_flag = fields.Boolean(default=False)
    
    def force_in_payment(self):
        for move in self:
            # If marked as paid, revert to in_payment
            if move.payment_state == 'paid':
                move.payment_state = 'in_payment'

    # @api.depends('move_type', 'line_ids.amount_residual')
    # def _compute_payments_widget_reconciled_info(self):
    #     super()._compute_payments_widget_reconciled_info()
    #     for move in self:
    #         if move.force_in_payment_flag and move.payment_state == 'paid':
    #             move.payment_state = 'in_payment'

    # @api.depends('amount_residual', 'state', 'line_ids.matched_debit_ids', 'line_ids.matched_credit_ids')
    # def _compute_payment_state(self):
    #     for move in self:
    #         if move.state != 'posted' or not move.is_invoice(include_receipts=True):
    #             move.payment_state = 'not_paid'
    #             continue
    #
    #         # CUSTOM: Override if force flag is set
    #         if getattr(move, 'force_in_payment_flag', False):
    #             move.payment_state = 'in_payment'
    #             continue
    #
    #         if not move.amount_residual:
    #             # Fully paid
    #             move.payment_state = 'paid'
    #         elif move.amount_residual == move.amount_total:
    #             # Not paid at all
    #             move.payment_state = 'not_paid'
    #         else:
    #             # Partial payment
    #             move.payment_state = 'partial'
                