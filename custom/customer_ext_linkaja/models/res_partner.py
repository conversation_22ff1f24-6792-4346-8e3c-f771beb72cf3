# -*- coding: utf-8 -*-
import logging

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)


class ResPartner(models.Model):
    _inherit = 'res.partner'


    is_customer = fields.Boolean('Is Customer', default=False)
    state = fields.Selection([
        ('draft', 'Draft'),
        ('submit', 'Submit'),
        ('validate_tax', 'Validate Tax'),
        ('assign_coa', 'Assign COA'),
        ('active', 'Active')
    ], string='Customer Status',default='draft', index=True, copy=False)

    type = fields.Selection(selection_add=[('other', 'Alamat NPWP')], string='company_type')
    ar_credit_note_id = fields.Many2one('account.account', company_dependent=True, 
        string='AR Credit Note', 
        domain="[('account_type', '=', 'asset_receivable'), ('deprecated', '=', False)]",
        ondelete='restrict')
    ar_other_id = fields.Many2one('account.account', company_dependent=True, 
        string='AR Others', 
        domain="[('account_type', '=', 'asset_receivable'), ('deprecated', '=', False)]",
        ondelete='restrict')
    
    employee_submit_id = fields.Many2one('hr.employee', string='Employee Submit')
    id_tku = fields.Char('ID TKU')
    id_pembeli = fields.Selection([
        ('tin', 'TIN'),
        ('nik', 'NIK'),
        ('passport', 'Passport'),
        ('others', 'Others'),
    ], string="ID Pembeli", default='tin')
    id_pembeli_passport = fields.Char('Passport')
    id_pembeli_others = fields.Char('Others')
    promote_spend_authorized = fields.Boolean(string="Promote Spend Authorized") #test
    is_edit_validate_tax = fields.Boolean(string='Edited Validate Tax', default=False)
    is_edit_coa = fields.Boolean(string='Edit COA,', default=False)

    @api.model
    def default_get(self, fields_list):
        res = super().default_get(fields_list)
        if 'type' in fields_list:
            res['type'] = 'contact'
        return res

    def button_edit_validate_tax(self):
        for rec in self:
            rec.state = 'validate_tax'
            rec.is_edit_validate_tax = True

    def button_edit_assign_coa(self):
        self.state = 'assign_coa'

    @api.onchange('vat')
    def _onchange_vat(self):
        if self.vat:
            self.id_tku = self.vat + '000000'
        else:
            self.id_tku = '000000'

    def button_action_submit(self):
        self.state = 'validate_tax'
        self.employee_submit_id = self.env.user.employee_id.id

        # send email to user tax
        group_tax = self.sudo().env.ref('customer_ext_linkaja.group_user_tax')
        if group_tax and group_tax.users:
            emails = group_tax.users.filtered(lambda x: x.employee_id.work_email).mapped('employee_id.work_email')
            if emails:
                email_to = ', '.join(emails)
                template = self._get_approval_email_template_tax()
                print(template, 'templateeeee')
                self.send_email_template(email_to, template)
    
    def button_action_validate_tax(self):
        # if self.state != 'submit':
        #     raise ValidationError(_('You can only validate tax after submitting.'))
        if not self.vat or not self.id_pembeli \
            or (self.id_pembeli and self.id_pembeli == 'nik' and not self.l10n_id_nik) \
            or (self.id_pembeli and self.id_pembeli == 'passport' and not self.id_pembeli_passport) \
            or (self.id_pembeli and self.id_pembeli == 'others' and not self.id_pembeli_others):
            
            raise ValidationError(_('NPWP or ID Pembeli is required.'))
            
        # self.state = 'assign_coa'
        if self.is_edit_validate_tax:
            if self.is_edit_coa == False:
                self.state = 'active'
                self.is_edit_validate_tax = False 

            else:
                self.state = 'assign_coa'
                self.is_edit_coa = False 
                self.is_edit_validate_tax = False 
        else:
            self.state = 'assign_coa'

        # send email to user accounting
        # group_acc = self.env.ref('account.group_account_invoice')
        group_acc = self.sudo().env.ref('customer_ext_linkaja.group_user_accounting')
        if group_acc and group_acc.users:
            emails = group_acc.users.filtered(lambda x: x.employee_id.work_email).mapped('employee_id.work_email')
            if emails:
                email_to = ', '.join(emails)
                template = self._get_approval_email_template_accounting()
                self.send_email_template(email_to, template)
    
    def button_action_assign_coa(self):
        # if self.state != 'validate_tax':
        #     raise ValidationError(_('You can only assign COA after validating tax.'))
        if not self.ar_credit_note_id:
            raise ValidationError(_('Account Receivable Credit Note is required.'))
        if not self.ar_other_id:
            raise ValidationError(_('Account Receivable Other is required.'))
        self.state = 'active'

        # send email to creator
        email_to = self.employee_submit_id.work_email
        template = self._get_approval_info_email_template()
        self.send_email_template(email_to, template)
    
    # method not used
    def action_active(self):
        if self.state != 'assign_coa':
            raise ValidationError(_('You can only activate after assigning COA.'))
        self.state = 'active'

    # send email
    def _get_approval_email_template_tax(self):
        """ function to return mail template for send group email """
        return 'customer_ext_linkaja.email_new_customer_tax'
    
    def _get_approval_email_template_accounting(self):
        """ function to return mail template for send group email """
        return 'customer_ext_linkaja.email_new_customer_accounting'
    
    def _get_approval_info_email_template(self):
        """ function to return mail template for send group email """
        return 'customer_ext_linkaja.email_new_customer_info'
    
    def _get_email_alias(self):
        """ function to get email alias """
        domain = [('alias_model_id.model', '=', 'res.partner')]
        alias = self.env['mail.alias'].search(domain, limit=1)
        return alias.display_name

    def send_email_template(self, email_to, template):
        """ function to send info email to requestor """
        # mail_from = self._get_email_alias()  # use alias to send email
        mail_from = self.env.user.email_formatted or self.env.user.employee_id.work_email

        # get attachment or empty list if none
        # attachment = self._get_attachment() or []
        # if attachment:
        #     attachment = [(6, 0, attachment.ids)]

        mail_to = email_to
        try:
            template_id = self.sudo().env.ref('%s' % template)
        except ValueError:
            template_id = False

        web_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        url_root = '%s/' % web_url  

        customer_link = '%sodoo/customers/%s' % (url_root, self.id)

        if mail_from and mail_to and template_id:
            # template_id.attachment_ids = attachment
            template_id.with_context(
                mail_from=mail_from,
                mail_to=mail_to,
                customer_link=customer_link
            ).send_mail(self.id, force_send=True)

        # cleanup
        # template_id.attachment_ids = [(3, x.id) for x in template_id.attachment_ids]

        return