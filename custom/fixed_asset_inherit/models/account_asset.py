from datetime import date
from odoo import api, fields, models, _
from odoo.tools import float_compare, float_is_zero
from odoo.exceptions import UserError, ValidationError

import calendar
from datetime import date, datetime
from dateutil.relativedelta import relativedelta


class AccountAsset(models.Model):
    _inherit = 'account.asset'


    def default_get_account_asset_id(self):
        if self.model_id and self.model_id.fixed_asset_account_id:
            return self.model_id.fixed_asset_account_id.id
        return False

    account_analytic_id = fields.Many2one(
        'account.analytic.account', 'Analytic Account')
    fixed_asset_account_id = fields.Many2one('account.account', string='Fixed Asset Account')
    account_asset_id = fields.Many2one(
        'account.account',
        default=lambda self: self.default_get_account_asset_id(), tracking=True)
    account_depreciation_id = fields.Many2one('account.account', tracking=True)
    account_depreciation_expense_id = fields.Many2one('account.account', tracking=True)
    journal_id = fields.Many2one('account.journal', tracking=True)
    account_asset_dua_id = fields.Many2one(
        'account.account',
        string='Fixed Asset Account'
    )
    method_number = fields.Integer(tracking=True)
    name = fields.Char(tracking=True)
    asset_no = fields.Char(tracking=True)
    origin_value = fields.Monetary(tracking=True)
    acquisition_date = fields.Date(tracking=True, readonly=False)
    model_id = fields.Many2one('account.asset', tracking=True)
    asset_group_id = fields.Many2one('account.asset.group', tracking=True)
    method = fields.Selection(tracking=True)
    method_period = fields.Selection(tracking=True)
    prorata_computation_type = fields.Selection(tracking=True, required=False)
    already_depreciation_amount_import = fields.Monetary(tracking=True)
    book_value = fields.Monetary('Book Value')
    stock_location_id = fields.Many2one('asset.location', string='Location')
    depreciation_entries_count = fields.Integer(compute='_entry_count', string='Depreciation Entries')
    original_value_currency = fields.Float('Original Value Currency')
    currency_rate = fields.Float('Rate')
    tag_number = fields.Char('Tag Number')
    cip_category_id = fields.Many2one('cip.configuration', string='CIP Category')
    cip_account_id = fields.Many2one('account.account', string='CIP Account', related="cip_category_id.account_id", readonly=False)
    account_asset_id = fields.Many2one(
        'account.account',
        string='Fixed Asset Account',
        compute=False,
        help="Account used to record the purchase of the asset at its original price.",
        store=True, readonly=False,
        check_company=True,
        domain="[('account_type', '!=', 'off_balance')]",
    )
    ir_sequence_id = fields.Many2one('ir.sequence', string='Sequence')
    assets_type_id = fields.Many2one('account.asset', string='Assets Type')

    assets_type = fields.Selection([
        ('intangible', 'Intangible'),
        ('non_intangible', 'Non Intangible')
    ], string='Asset Type', default='intangible')
    display_account_asset_id = fields.Boolean('Display Account Asset', store=False, compute="_compute_new_display_account_asset_id")
    cip_clearing_account_id = fields.Many2one('account.account', string='CIP CLearing Account')


    @api.depends('state')
    def _compute_new_display_account_asset_id(self):
        for record in self:
            record.display_account_asset_id = record.state != 'model'

    # @api.onchange('cip_category_id')
    # def _onchange_cip_category_id(self):
    #     for record in self:
    #         record.cip_account_id = record.cip_category_id.account_id.id

    @api.constrains('active', 'state')
    def _check_active(self):
        for record in self:
            if not record.active and record.state not in ('close', 'model'):
                raise UserError(_('You cannot archive a record that is not closed'))

    @api.model_create_multi
    def create(self, vals):
        for val in vals:
            if not val.get('name') and val.get('state') != 'model':
                val['name'] = self.env['ir.sequence'].next_by_code('account.asset.seq')
            if not val.get('asset_no') and val.get('state') != 'model' and val.get('asset_type_id'):
                code = val.get('asset_type_id').code
                val['ir_sequence_id'] = self.env['ir.sequence'].next_by_code(code)

        res = super(AccountAsset, self).create(vals)
        return res

    # def write(self, vals):
    #     res = super(AccountAsset, self).write(vals)
    #     if 'cip_account_id' in vals:
    #         for record in self:
    #             if record.cip_category_id:
    #                 record.cip_category_id.account_id = record.cip_account_id
    #     return res

    @api.onchange('model_id')
    def _onchange_model_id(self):
        for rec in self:
            rec.account_asset_id = rec.model_id.fixed_asset_account_id
            rec.method = rec.model_id.method
            rec.method_number = rec.model_id.method_number
            rec.method_period = rec.model_id.method_period
            rec.prorata_computation_type = rec.model_id.prorata_computation_type
            rec.account_depreciation_id = rec.model_id.account_depreciation_id
            rec.account_depreciation_expense_id = rec.model_id.account_depreciation_expense_id
            rec.journal_id = rec.model_id.journal_id

    def validate(self):
        # self.compute_depreciation_board()

        # check period first
        self._assign_period()

        # if self.asset_no == '/':
        #     self.asset_no = self.env['ir.sequence'].next_by_code('asset.number')
        fields = [
            'method',
            'method_number',
            'method_period',
            'method_progress_factor',
            'salvage_value',
            'original_move_line_ids',
        ]
        ref_tracked_fields = self.env['account.asset'].fields_get(fields)
        for asset in self:
            for source in asset.source_line_ids:
                if source.invoice_date and asset.acquisition_date < source.invoice_date:
                    raise ValidationError(_('Acquisition date cannot be earlier than invoice date.'))

            tracked_fields = ref_tracked_fields.copy()
            if asset.method == 'linear':
                tracked_fields.pop('method_progress_factor', None)
            initial_values = {asset.id: {field: asset[field] for field in fields}}
            tracking_value_ids = asset._message_track(tracked_fields, initial_values)
            asset_name = {
                'purchase': (_('Asset created'), _('An asset has been created for this move:')),
                'sale': (_('Deferred revenue created'), _('A deferred revenue has been created for this move:')),
                'expense': (_('Deferred expense created'), _('A deferred expense has been created for this move:')),
            }.get(asset.asset_type, (_('Asset created'), _('An asset has been created for this move:')))

            msg = '%s <a href=# data-oe-model=account.asset data-oe-id=%d>%s</a>' % (
                asset_name[1], asset.id, asset.name
            )

            # Post message with tracking or fallback
            if isinstance(tracking_value_ids, list) and all(isinstance(v, (list, tuple)) for v in tracking_value_ids):
                asset.message_post(body=asset_name[0], tracking_value_ids=tracking_value_ids)
            else:
                asset.message_post(body=asset_name[0])

            # Tambahkan message di journal entry original
            for move_id in asset.original_move_line_ids.mapped('move_id'):
                move_id.message_post(body=msg)

            # Hitung depreciation kalau belum ada
            if not asset.depreciation_line_ids:
                asset.compute_depreciation_list()

            asset._check_depreciations()
            asset.write({'state': 'open'})

    def _compute_board_undone_dotation_nb(self, depreciation_date, total_days):
        undone_dotation_number = self.method_number
        # if self.method_time == 'end':
        #     end_date = self.method_end
        #     undone_dotation_number = 0
        #     while depreciation_date <= end_date:
        #         depreciation_date = date(depreciation_date.year, depreciation_date.month,
        #                                  depreciation_date.day) + relativedelta(months=+self.method_period)
        #         undone_dotation_number += 1
        if self.prorata_computation_type:
            undone_dotation_number += 1
        return undone_dotation_number

    def _compute_board_amount_dotation(
        self, sequence, residual_amount, amount_to_depr, undone_dotation_number,
        posted_depreciation_line_ids, total_days, depreciation_date):
        amount = 0
        if sequence == undone_dotation_number:
            amount = residual_amount
        else:
            if self.method == 'linear':
                # original compute amount by odoo
                # amount = amount_to_depr / (undone_dotation_number - len(posted_depreciation_line_ids))

                # modify to recompute depreciation base on original value when change method number.
                # amount = self.original_value / undone_dotation_number
                # NOTE: change to book_value
                amount = self.book_value / undone_dotation_number
                print(amount)
                if self.prorata_computation_type:
                    amount = amount_to_depr / self.method_number
                    if sequence == 1:
                        depreciation_date = self.acquisition_date
                        if int(self.method_period) % 12 != 0:
                            month_days = calendar.monthrange(depreciation_date.year, depreciation_date.month)[1]
                            days = month_days - depreciation_date.day + 1
                            amount = (amount_to_depr / self.method_number) / month_days * days
                        else:
                            days = (self.company_id.compute_fiscalyear_dates(depreciation_date)['date_to'] - depreciation_date).days + 1
                            amount = (amount_to_depr / self.method_number) / total_days * days
            elif self.method == 'degressive':
                amount = residual_amount * self.method_progress_factor
                if self.prorata_computation_type:
                    if sequence == 1:
                        depreciation_date = self.acquisition_date
                        if int(self.method_period) % 12 != 0:
                            month_days = calendar.monthrange(depreciation_date.year, depreciation_date.month)[1]
                            days = month_days - depreciation_date.day + 1
                            amount = (residual_amount * self.method_progress_factor) / month_days * days
                        else:
                            days = (self.company_id.compute_fiscalyear_dates(depreciation_date)['date_to'] - depreciation_date).days + 1
                            amount = (residual_amount * self.method_progress_factor) / total_days * days
        return amount

    def compute_depreciation_list(self):
        self.ensure_one()

        posted_depreciation_line_ids = self.depreciation_line_ids.filtered(lambda x: x.move_check).sorted(key=lambda l: l.depreciation_date)
        unposted_depreciation_line_ids = self.depreciation_line_ids.filtered(lambda x: not x.move_check)

        # Remove old unposted depreciation lines. We cannot use unlink() with One2many field
        commands = [(2, line_id.id, False) for line_id in unposted_depreciation_line_ids]

        if self.value_residual != 0.0:
            amount_to_depr = residual_amount = self.value_residual
            # if we already have some previous validated entries, starting date is last entry + method period
            if posted_depreciation_line_ids and posted_depreciation_line_ids[-1].depreciation_date:
                last_depreciation_date = fields.Date.from_string(posted_depreciation_line_ids[-1].depreciation_date)
                depreciation_date = last_depreciation_date + relativedelta(months=+int(self.method_period))
            else:
                # depreciation_date computed from the purchase date
                depreciation_date = self.acquisition_date
                # if self.first_depreciation_date and self.first_depreciation_date != self.acquisition_date:
                #     depreciation_date = self.first_depreciation_date
                    # depreciation_date set manually from the 'first_depreciation_manual_date' field

            total_days = (depreciation_date.year % 4) and 365 or 366
            month_day = depreciation_date.day
            undone_dotation_number = self._compute_board_undone_dotation_nb(depreciation_date, total_days)

            amount_to_depr = residual_amount = self.book_value  # NOTE: use book_value as depreciation base
            for x in range(len(posted_depreciation_line_ids), undone_dotation_number):
                sequence = x + 1
                # original compute odoo
                # amount = self._compute_board_amount_dotation(
                #     sequence, residual_amount, amount_to_depr,
                #     undone_dotation_number, posted_depreciation_line_ids,
                #     total_days, depreciation_date)

                # new modify to recompute depreciation
                count_posted = len(posted_depreciation_line_ids)
                total_posted_deprciation = 0
                for rec_post_depreciation in posted_depreciation_line_ids:
                    total_posted_deprciation += rec_post_depreciation.amount

                if count_posted + 1 == sequence:
                    new_amount = self._compute_board_amount_dotation(
                        sequence, residual_amount, amount_to_depr,
                        undone_dotation_number, posted_depreciation_line_ids,
                        total_days, depreciation_date)
                    adj_depr = (new_amount * count_posted) - total_posted_deprciation
                    amount = new_amount + adj_depr
                else:
                    amount = self._compute_board_amount_dotation(
                            sequence, residual_amount, amount_to_depr,
                            undone_dotation_number, posted_depreciation_line_ids,
                            total_days, depreciation_date)
                # end new modify to recompute depreciation

                amount = self.currency_id.round(amount)
                if float_is_zero(amount, precision_rounding=self.currency_id.rounding):
                    continue
                residual_amount -= amount
                vals = {
                    'amount': amount,
                    'asset_id': self.id,
                    'sequence': sequence,
                    'name': self.model_id.display_name,
                    'remaining_value': residual_amount,
                    'depreciated_value': self.book_value - (self.salvage_value + residual_amount),
                    'depreciation_date': depreciation_date,
                }
                commands.append((0, False, vals))
                depreciation_date = depreciation_date + relativedelta(months=+int(self.method_period))

                # if month_day > 28 and self.date_first_depreciation == 'manual':
                #     max_day_in_month = calendar.monthrange(depreciation_date.year, depreciation_date.month)[1]
                #     depreciation_date = depreciation_date.replace(day=min(max_day_in_month, month_day))

                # # datetime doesn't take into account that the number of days is not the same for each month
                # if not self.prorata_computation_type and self.method_period % 12 != 0 and self.date_first_depreciation == 'last_day_period':
                #     max_day_in_month = calendar.monthrange(depreciation_date.year, depreciation_date.month)[1]
                #     depreciation_date = depreciation_date.replace(day=max_day_in_month)

        self.write({'depreciation_line_ids': commands})
        return True
    
    def _entry_count(self):
        for asset in self:
            return len(asset.depreciation_line_ids)

    def open_entries(self):
        move_ids = []
        for asset in self:
            for depreciation_line in asset.depreciation_move_ids:
                move_ids.append(depreciation_line.id)
                # if depreciation_line.state == 'posted':
                    # move_ids.append(depreciation_line.id)
        return {
            'name': _('Journal Entries'),
            'view_type': 'form',
            'view_mode': 'list,form',
            'res_model': 'account.move',
            'view_id': False,
            'type': 'ir.actions.act_window',
            'domain': [('id', 'in', move_ids)],
        }

    
    def action_move_create(self, partner_id=False, date_acc=False):
        """ function to create and post journal entry """
        context = self._context
        credit_account = self.with_context(context)._get_credit_account()
        debit_account = self.with_context(context)._get_debit_account()
        journal = self.with_context(context)._get_journal()
        name = self.with_context(context)._get_asset_name()
        move_date = date_acc if date_acc else date.today()
        move_lines = self.with_context(context)._create_move_lines(
            name, partner_id, debit_account, credit_account)
        if context.get('from_cip'):
            ref = "GENERATE CIP %s" % name
            narration = "CIP %s" % name
        else:
            ref = "GENERATE ASSET %s" % name
            narration = "Asset %s" % name
        move_dict = {
            'narration': narration,
            'ref': ref,
            'journal_id': journal,
            'date': move_date,
            'line_ids': move_lines,  # <-- wrap with (0, 0, ...)
        }
        move = self.env['account.move'].create(move_dict)
        move._post()  # direclty post
        return True

    def action_move_create_multi_credit(self, debit_account_id=None, debit_amount=None, credit_accounts=None, partner_id=False, date_acc=False):
        """ function to create and post journal entry with multiple credit accounts """
        context = self._context
        name = self.with_context(context)._get_asset_name()
        move_date = date_acc if date_acc else date.today()
        
        # Handle new context structure with journals_data
        journals_data = context.get('journals_data', [])
        if journals_data:
            # Create separate journal entries for each journal
            for journal_data in journals_data:
                journal_id = journal_data['journal_id']
                credit_account_id = journal_data['credit_account_id']
                debit_account_id = journal_data.get('debit_account_id')
                amount = journal_data['amount']
                
                # Validate that accounts exist
                if not journal_id:
                    raise ValueError("Missing journal_id in journal_data")
                if not credit_account_id:
                    raise ValueError("Missing credit_account_id in journal_data")
                if not debit_account_id:
                    raise ValueError("Missing debit_account_id in journal_data")
                if not amount or amount <= 0:
                    raise ValueError("Invalid amount in journal_data")
                
                # Create move lines for this journal
                move_lines = []
                
                # Debit line
                debit_line = {
                    'name': name,
                    'partner_id': partner_id.id if partner_id else False,
                    'account_id': debit_account_id,
                    'journal_id': journal_id,
                    'date': move_date,
                    'debit': amount,
                    'credit': 0,
                    'analytic_account_id': self.account_analytic_id.id if hasattr(self, 'account_analytic_id') and self.account_analytic_id else False,
                }
                move_lines.append((0, 0, debit_line))
                
                # Credit line
                credit_line = {
                    'name': name,
                    'partner_id': partner_id.id if partner_id else False,
                    'account_id': credit_account_id,
                    'journal_id': journal_id,
                    'date': move_date,
                    'debit': 0,
                    'credit': amount,
                    'analytic_account_id': self.account_analytic_id.id if hasattr(self, 'account_analytic_id') and self.account_analytic_id else False,
                }
                move_lines.append((0, 0, credit_line))
                
                if context.get('from_cip'):
                    ref = "GENERATE CIP %s" % name
                    narration = "CIP %s" % name
                else:
                    ref = "GENERATE ASSET %s" % name
                    narration = "Asset %s" % name
                    
                move_dict = {
                    'narration': narration,
                    'ref': ref,
                    'journal_id': journal_id,
                    'date': move_date,
                    'line_ids': move_lines,
                }
                move = self.env['account.move'].create(move_dict)
                move._post()  # directly post
        else:
            # Fallback to original logic for backward compatibility
            journal = self.with_context(context)._get_journal()
            
            # Create move lines with one debit and multiple credits
            move_lines = self.with_context(context)._create_move_lines_multi_credit(
                name, partner_id, debit_account_id, debit_amount, credit_accounts)
            
            if context.get('from_cip'):
                ref = "GENERATE CIP %s" % name
                narration = "CIP %s" % name
            else:
                ref = "GENERATE ASSET %s" % name
                narration = "Asset %s" % name
                
            move_dict = {
                'narration': narration,
                'ref': ref,
                'journal_id': journal,
                'date': move_date,
                'line_ids': move_lines,
            }
            move = self.env['account.move'].create(move_dict)
            move._post()  # directly post
        return True

    def _create_move_lines_multi_credit(self, name, partner, debit_account_id, debit_amount, credit_accounts):
        """ function to generate move lines with multiple credit accounts """
        journal = self._context.get('journal_id', False)
        if not journal:
            journal = self.model_id.journal_id.id
        
        # Create debit line
        debit_line = {
            'name': name,
            'partner_id': partner.id if partner else False,
            'account_id': debit_account_id,
            'journal_id': journal,
            'date': date.today(),
            'debit': debit_amount,
            'credit': 0,
            'analytic_account_id': self.account_analytic_id.id,
        }
        
        move_lines = [(0, 0, debit_line)]
        
        # Create multiple credit lines - distribute amount proportionally
        total_original_amount = sum(details['amount'] for details in credit_accounts.values())
        remaining_amount = debit_amount
        
        credit_account_items = list(credit_accounts.items())
        for i, (account, details) in enumerate(credit_account_items):
            if i == len(credit_account_items) - 1:  # Last item gets remaining amount
                credit_amount = remaining_amount
            else:
                # Proportional distribution
                credit_amount = round((details['amount'] / total_original_amount) * debit_amount, 2)
                remaining_amount -= credit_amount
            
            credit_line = {
                'name': name,
                'partner_id': partner.id if partner else False,
                'account_id': account.id,
                'journal_id': journal,
                'date': date.today(),
                'debit': 0,
                'credit': credit_amount,
                'analytic_account_id': self.account_analytic_id.id,
            }
            move_lines.append((0, 0, credit_line))
        
        return move_lines

    @api.depends('acquisition_date', 'company_id', 'prorata_computation_type')
    def _compute_prorata_date(self):
        for asset in self:
            if asset.prorata_computation_type == 'none' and asset.acquisition_date:
                fiscalyear_date = asset.company_id.compute_fiscalyear_dates(asset.acquisition_date).get('date_from')
                asset.prorata_date = fiscalyear_date
            elif asset.acquisition_date:
                asset.prorata_date = asset.acquisition_date.replace(day=1)
            else:
                asset.prorata_date = False

    def _get_debit_account(self):
        """ helper function to get debit account """
        context = self._context
        account = context.get('debit_account_id', False)
        product = context.get('product_id', False)
        if not account:
            account = self.model_id.account_asset_id.id

            if not account:
                if product:
                    # move will be created using journal from asset model, credit account
                    # taken from the stock journal of each asset detail, debit account from
                    # asset model expense account, value from assset detail amount
                    # to get the valuation journal from picking
                    # clue: loop picking.move_line_ids.filtered with no reversed_entry_id
                    # then line_ids filtered with debit value and product is same as product_id
                    picking = self.picking_id
                    move = picking.move_ids_without_package.filtered(lambda x: x.product_id == product)[:1]
                    account = move.account_move_ids.line_ids.account_id[1].id
        return account

    def _compute_board_amount(self, residual_amount, period_start_date, period_end_date, days_already_depreciated,
                              days_left_to_depreciated, residual_declining, start_yearly_period=None, total_lifetime_left=None,
                              residual_at_compute=None, start_recompute_date=None):

        def _get_max_between_linear_and_degressive(linear_amount):
            """
            Compute the degressive amount that could be depreciated and returns the biggest between it and linear_amount
            The degressive amount corresponds to the difference between what should have been depreciated at the end of
            the period and the residual_amount (to deal with rounding issues at the end of each month)
            """
            fiscalyear_dates = self.company_id.compute_fiscalyear_dates(period_end_date)
            days_in_fiscalyear = self._get_delta_days(fiscalyear_dates['date_from'], fiscalyear_dates['date_to'])

            degressive_total_value = residual_declining * (1 - self.method_progress_factor * self._get_delta_days(start_yearly_period, period_end_date) / days_in_fiscalyear)
            degressive_amount = residual_amount - degressive_total_value
            return self._degressive_linear_amount(residual_amount, degressive_amount, linear_amount)

        days_until_period_end = self._get_delta_days(self.paused_prorata_date, period_end_date)
        days_before_period = self._get_delta_days(self.paused_prorata_date, period_start_date + relativedelta(days=-1))
        days_before_period = max(days_before_period, 0)  # if disposed before the beginning of the asset for example
        number_days = days_until_period_end - days_before_period
        if float_is_zero(self.asset_lifetime_days, 2):
            return 0, 0

        # The amount to depreciate are computed by computing how much the asset should be depreciated at the end of the
        # period minus how much difference it is actually depreciated. It is done that way to avoid having the last move to take
        # every single small difference that could appear over the time with the classic computation method.
        if self.method == 'linear':
            if total_lifetime_left and float_compare(total_lifetime_left, 0, 2) > 0:
                computed_linear_amount = residual_amount - residual_at_compute * (1 - self._get_delta_days(start_recompute_date, period_end_date) / total_lifetime_left)
            else:
                computed_linear_amount = self._get_linear_amount(days_before_period, days_until_period_end, self.total_depreciable_value)
            amount = min(computed_linear_amount, residual_amount, key=abs)
        elif self.method == 'degressive':
            # Linear amount
            # We first calculate the total linear amount for the period left from the beginning of the year
            # to get the linear amount for the period in order to avoid big delta at the end of the period
            days_left_from_beginning_of_year = self._get_delta_days(start_yearly_period, period_start_date - relativedelta(days=1)) + days_left_to_depreciated
            expected_remaining_value_with_linear = residual_declining - residual_declining * self._get_delta_days(start_yearly_period, period_end_date) / days_left_from_beginning_of_year if days_left_from_beginning_of_year != 0 else 0
            linear_amount = residual_amount - expected_remaining_value_with_linear

            amount = _get_max_between_linear_and_degressive(linear_amount)
        elif self.method == 'degressive_then_linear':
            if not self.parent_id:
                linear_amount = self._get_linear_amount(days_before_period, days_until_period_end, self.total_depreciable_value)
            else:
                # we want to know the amount before the reeval for the parent so the child can follow the same curve,
                # so it transitions from degressive to linear at the same moment
                parent_moves = self.parent_id.depreciation_move_ids.filtered(lambda mv: mv.date <= self.prorata_date).sorted(key=lambda mv: (mv.date, mv.id))
                parent_cumulative_depreciation = parent_moves[-1].asset_depreciated_value if parent_moves else self.parent_id.already_depreciated_amount_import
                parent_depreciable_value = parent_moves[-1].asset_remaining_value if parent_moves else self.parent_id.total_depreciable_value
                if self.currency_id.is_zero(parent_depreciable_value):
                    linear_amount = self._get_linear_amount(days_before_period, days_until_period_end, self.total_depreciable_value)
                else:
                    # To have the same curve as the parent, we need to have the equivalent amount before the reeval.
                    # The child's depreciable value corresponds to the amount that is left to depreciate for the parent.
                    # So, we use the proportion between them to compute the equivalent child's total to depreciate.
                    # We use it then with the duration of the parent to compute the depreciation amount
                    depreciable_value = self.total_depreciable_value * (1 + parent_cumulative_depreciation/parent_depreciable_value)
                    linear_amount = self._get_linear_amount(days_before_period, days_until_period_end, depreciable_value) * self.asset_lifetime_days / self.parent_id.asset_lifetime_days

            amount = _get_max_between_linear_and_degressive(linear_amount)

        amount = max(amount, 0) if self.currency_id.compare_amounts(residual_amount, 0) > 0 else min(amount, 0)
        amount = self._get_depreciation_amount_end_of_lifetime(residual_amount, amount, days_until_period_end)

        return number_days, self.currency_id.round(amount)




class AssetLocation(models.Model):
    _inherit = 'asset.location'

    asset_count = fields.Integer(
        string="Assets Count",
        compute='_compute_asset_count',
        help="Number of assets linked to this location"
    )

    def _compute_asset_count(self):
        for location in self:
            location.asset_count = self.env['account.asset'].search_count([
                ('stock_location_id', '=', location.id)
            ])

    def action_view_assets(self):
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': 'Assets',
            'res_model': 'account.asset',
            'view_mode': 'list,form',
            'domain': [('stock_location_id', '=', self.id)],
            'context': {'default_stock_location_id': self.id},
            'target': 'current',
        }