<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="procurement_view_form" model="ir.ui.view">
            <field name="name">procurement.update.data.form</field>
            <field name="model">procurement.update.data</field>
            <field name="inherit_id" ref="partner_linkaja.procurement_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//sheet" position="after">
                    <chatter/>
                </xpath>
                <xpath expr="//header/button[@name='action_approve']" position="replace" />
                
                <xpath expr="//field[@name='company_name']" position="after">
                    <field name="requestor_id" required="1" />
                </xpath>
                <!-- add button to do action -->
                <xpath expr="//header" position="inside">
                    <button name="action_approval" type="object" string="Approve" class="btn btn-success"
                        invisible="procurement_state != 'pending_approval' or not is_current_approver"
                        context="{'approve': True}" />
                    <button name="action_approval" type="object" string="Reject" class="btn-danger"
                        invisible="procurement_state != 'pending_approval' or not is_current_approver"
                        context="{'reject': True}" />
                    <button name="action_approval" type="object" string="Reassign" class="btn btn-success"
                        invisible="procurement_state != 'pending_approval' or (not is_current_approver and not is_admin)"
                        context="{'reassign': True}" />
                    <button name="approve_bypass" type="object" string="Approve Bypass" class="btn btn-success"
                        invisible="procurement_state != 'draft'" 
                        groups="justif_approval.administrator_setting" />
                </xpath>

                <!-- add approval_history_ids in new page inside notebook -->
                <xpath expr="//notebook" position="inside">
                    <page name="approval_history" string="Approvals" invisible="1">
                        <field name="is_current_approver" invisible="1" />
                        <field name="selected_approver_ids" widget="many2many_tags" invisible="1"/>
                        <field name="approval_history_ids" readonly="1" force_save='1' />
                    </page>
                    <page string="Approval Details" name="history_detail">
                        <field name="approval_history_detail_ids" >
                            <list editable="bottom" create="0" edit="0" delete="0">
                                <field name="level" readonly="1" force_save='1' />
                                <field name="employee_id" readonly="1" force_save='1' />
                                <field name="employee_state" readonly="1" force_save='1' />
                                <field name="employee_date" readonly="1" force_save='1' />
                                <field name="employee_note" readonly="1" force_save='1' />
                                <field name="reassign_employee_id" readonly="1" force_save='1' />
                                <field name="reassign_employee_state" readonly="1" force_save='1' />
                                <field name="reassign_employee_date" readonly="1" force_save='1' />
                                <field name="reassign_employee_note" readonly="1" force_save='1' />
                            </list>
                        </field>
                    </page>
                    <page name="approval_message" string="Approval Messages">
                        <field name="approval_message_ids" readonly="1" force_save='1' />
                    </page>
                </xpath>

                <xpath expr="//field[@name='company_address']" position="after">
                    <field name="create_uid" />
                    <field name="hierarchy_id" readonly="1" force_save='1' />
                </xpath>
            </field>
        </record>

        <record id="view_procurement_update_filter" model="ir.ui.view">
            <field name="name">procurement.update.data.filter</field>
            <field name="model">procurement.update.data</field>
            <field name="inherit_id" ref="partner_linkaja.view_procurement_update_filter"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='reference_number']" position="after">
                    <separator/>
                    <filter name="my_approvals" string="My Approvals" domain="[('selected_approver_ids.user_id', '=', uid)]" />
                    <separator/>
                </xpath>
            </field>
        </record>

        <record id="partner_linkaja.action_procurement_update_data" model="ir.actions.act_window">
            <field name="context">{'search_default_my_approvals': 1}</field>
            <field name="path">procurement_update</field>
        </record>
    </data>
</odoo>
