<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_partner_form" model="ir.ui.view">
            <field name="name">res.partner.base.form</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.view_partner_form"/>
            <field name="arch" type="xml">
                <!-- add button to do action -->
                <xpath expr="//header" position="inside">
                    <button name="action_approval" type="object" string="Approve" class="btn btn-success"
                        invisible="not context.get('vendor_view', False) or vendor_state not in ('submit', 'duediligence') or not is_current_approver"
                        context="{'approve': True}" />
                    <button name="action_approval" type="object" string="Reject" class="btn-danger"
                        invisible="not context.get('vendor_view', False) or vendor_state not in ('submit', 'duediligence') or not is_current_approver"
                        context="{'reject': True}" />
                    <button name="action_approval" type="object" string="Reassign" class="btn btn-success"
                        invisible="not context.get('vendor_view', False) or vendor_state not in ('submit', 'duediligence') or (not is_current_approver and not is_admin)"
                        context="{'reassign': True}" />
                    <button name="approve_bypass" type="object" string="Approve Bypass" class="btn btn-success"
                        invisible="not context.get('vendor_view', False) or vendor_state != 'draft'" 
                        groups="justif_approval.administrator_setting" />
                </xpath>

                <!-- add approval_history_ids in new page inside notebook -->

                <xpath expr="//field[@name='created_by']" position="after">
                    <field name="hierarchy_id" readonly="1" force_save='1' invisible="not context.get('vendor_view', False)" />
                    <field name="hierarchy_authorized_id" readonly="1" force_save='1' invisible="not context.get('vendor_view', False)" />
                </xpath>
            </field>
        </record>

        <record id="inherit_view_partner_property_form_followup_hide_id_inherit_partner_linkaja" model="ir.ui.view">
            <field name="name">res.partner.view.form.inherit</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="partner_linkaja.view_partner_property_form_followup_hide"/>
            <field name="arch" type="xml">
                <xpath expr="//page[@name='approval_prospective']" position="after">
                    <page name="approval_history" string="Approval Prospective" invisible="not context.get('vendor_view', False)">
                        <field name="is_current_approver" invisible="1" />
                        <field name="selected_approver_ids" widget="many2many_tags" invisible="1"/>
                        <field name="approval_history_ids" readonly="1" force_save='1' />
                    </page>
                    <page string="Approval Details Prospective" name="history_detail" invisible="not context.get('vendor_view')">
                        <field name="approval_history_detail_ids" >
                            <list editable="bottom" create="0" edit="0" delete="0">
                                <field name="level" readonly="1" force_save='1' />
                                <field name="employee_id" readonly="1" force_save='1' />
                                <field name="employee_state" readonly="1" force_save='1' />
                                <field name="employee_date" readonly="1" force_save='1' />
                                <field name="employee_note" readonly="1" force_save='1' />
                                <field name="reassign_employee_id" readonly="1" force_save='1' />
                                <field name="reassign_employee_state" readonly="1" force_save='1' />
                                <field name="reassign_employee_date" readonly="1" force_save='1' />
                                <field name="reassign_employee_note" readonly="1" force_save='1' />
                            </list>
                        </field>
                    </page>
                    <page string="Approval Authorized" name="approval_prospective2" invisible="not context.get('vendor_view')">
                        <field name="approval_ids" mode="list" readonly='1' force_save='1' >
                            <list>
                                <field name="level"/>
                                <field name="approval_by"/>
                                <field name="department_ids" widget="many2many_tags"/>
                                <field name="job_ids" widget="many2many_tags"/>
                                <field name="job_level"/>
                                <field name="employee_ids" widget="many2many_tags"/>
                                <field name="approval_type"/>
                                <field name="voting_point"/>
                                <field name="total_voting_point"/>
                                <field name="state"/>
                            </list>
                            <form>
                                <sheet>
                                    <group>
                                        <field name="level"/>
                                        <field name="approval_by"/>
                                        <field name="department_ids" widget="many2many_tags"/>
                                        <field name="job_ids" widget="many2many_tags"/>
                                        <field name="job_level"/>
                                        <field name="employee_ids" widget="many2many_tags"/>
                                        <field name="approval_type"/>
                                        <field name="voting_point"/>
                                        <field name="total_voting_point"/>
                                        <field name="state"/>
                                    </group>
                                </sheet>
                            </form>
                        </field>
                        <!-- <field name="approval_ids" readonly="1" force_save='1' /> -->

                    </page>
                    <page string="Approval Details Authorized" name="history_detail_authorized2" invisible="not context.get('vendor_view')">
                        <field name="approval_history_detail_authorized_ids" >
                            <list editable="bottom" create="0" edit="0" delete="0">
                                <field name="level" readonly="1" force_save='1' />
                                <field name="employee_id" readonly="1" force_save='1' />
                                <field name="employee_state" readonly="1" force_save='1' />
                                <field name="employee_date" readonly="1" force_save='1' />
                                <field name="employee_note" readonly="1" force_save='1' />
                                <field name="reassign_employee_id" readonly="1" force_save='1' />
                                <field name="reassign_employee_state" readonly="1" force_save='1' />
                                <field name="reassign_employee_date" readonly="1" force_save='1' />
                                <field name="reassign_employee_note" readonly="1" force_save='1' />
                            </list>
                        </field>
                    </page>
                    <page name="approval_message" string="Approval Messages" invisible="not context.get('vendor_view', False)">
                        <field name="approval_message_ids" readonly="1" force_save='1' />
                    </page>
                    <!-- only due dilligence for non procurement -->
                    <page string="Approval Due Diligence" name="approval_prospective_details2" invisible="not context.get('vendor_view') or procurement_unit">
                        <field name="can_edit_due_diligence" invisible="1"/>
                        <field name="procurement_unit" invisible="1"/>
                        <field name="fraud_unit" invisible="1"/>
                        <field name="compilance_unit" invisible="1"/>
                        <field name="hc_unit" invisible="1"/>
                        <field name="is_all_detail_filled" invisible="1"/>
                        <field name="approval_detail_ids" mode="list" force_save='1' >
                            <list editable="bottom" create='0' delete='0'>
                                <field name="procurement_unit" column_invisible="1"/>
                                <field name="fraud_unit" column_invisible="1"/>
                                <field name="compilance_unit" column_invisible="1"/>
                                <field name="procurement_unit" column_invisible="1"/>
                                <!-- <field name="unit_department_due_dill" column_invisible="0"/> -->
                                <field name="sequence" string="No" readonly="not parent.procurement_unit or parent.vendor_state != 'duediligence'"/>
                                <field name="name" string="Nama" readonly="not parent.procurement_unit or parent.vendor_state != 'duediligence'"/>
                                <field name="birth_date" string="Tanggal Lahir" readonly="not parent.procurement_unit or parent.vendor_state != 'duediligence'"/>
                                <field name="bl_internal" string="Procurement BL Internal (Y/N)" readonly="not parent.procurement_unit or parent.vendor_state != 'duediligence'"/>
                                <field name="media_info" string="Procurement Informasi Negatif Media (Clear/Not)" readonly="not parent.procurement_unit or parent.vendor_state != 'duediligence'"/>
                                <field name="fraud_data" string="Fraud Data Fraud (Clear/Not)" readonly="not parent.fraud_unit or parent.vendor_state != 'duediligence'"/>
                                <field name="apu_ppt" string="Compliance APU-PPT (Clear/Not)" readonly="not parent.compilance_unit or parent.vendor_state != 'duediligence'"/>
                                <field name="employee_status" string="Human Capital Status" readonly="not parent.hc_unit or parent.vendor_state != 'duediligence'"/>
                                <field name="note" readonly="not parent.procurement_unit or parent.vendor_state != 'duediligence'"/>
                            </list>
                            <!-- <form>
                                <sheet>
                                    <group>
                                        <field name="name" required="1"/>
                                        <field name="birth_date"/>
                                        <field name="bl_internal"/>
                                        <field name="media_info"/>
                                        <field name="fraud_data"/>
                                        <field name="apu_ppt"/>
                                        <field name="employee_status"/>
                                        <field name="note" widget="text"/>
                                    </group>
                                </sheet>
                            </form> -->
                        </field>
                    </page>
                    <!-- only due dilligence for procurement -->
                    <page string="Approval Due Diligence" name="approval_prospective_details3" invisible="not context.get('vendor_view') or not procurement_unit">
                        <field name="can_edit_due_diligence" invisible="1"/>
                        <field name="procurement_unit" invisible="1"/>
                        <field name="fraud_unit" invisible="1"/>
                        <field name="compilance_unit" invisible="1"/>
                        <field name="hc_unit" invisible="1"/>
                        <field name="is_all_detail_filled" invisible="1"/>
                        <field name="approval_detail_ids" mode="list" force_save='1' >
                            <list editable="bottom" >
                                <field name="procurement_unit" column_invisible="1"/>
                                <field name="fraud_unit" column_invisible="1"/>
                                <field name="compilance_unit" column_invisible="1"/>
                                <field name="procurement_unit" column_invisible="1"/>
                                <!-- <field name="unit_department_due_dill" column_invisible="0"/> -->
                                <field name="sequence" string="No" readonly="not parent.procurement_unit or parent.vendor_state != 'duediligence'" />
                                <field name="name" string="Nama" readonly="not parent.procurement_unit or parent.vendor_state != 'duediligence'"/>
                                <field name="birth_date" string="Tanggal Lahir" readonly="not parent.procurement_unit or parent.vendor_state != 'duediligence'"/>
                                <field name="bl_internal" string="Procurement BL Internal (Y/N)" readonly="not parent.procurement_unit or parent.vendor_state != 'duediligence'"/>
                                <field name="media_info" string="Procurement Informasi Negatif Media (Clear/Not)" readonly="not parent.procurement_unit or parent.vendor_state != 'duediligence'"/>
                                <field name="fraud_data" string="Fraud Data Fraud (Clear/Not)" readonly="not parent.fraud_unit or parent.vendor_state != 'duediligence'"/>
                                <field name="apu_ppt" string="Compliance APU-PPT (Clear/Not)" readonly="not parent.compilance_unit or parent.vendor_state != 'duediligence'"/>
                                <field name="employee_status" string="Human Capital Status" readonly="not parent.hc_unit or parent.vendor_state != 'duediligence'"/>
                                <field name="note" readonly="not parent.procurement_unit or parent.vendor_state != 'duediligence'"/>
                            </list>
                            <!-- <form>
                                <sheet>
                                    <group>
                                        <field name="name" required="1"/>
                                        <field name="birth_date"/>
                                        <field name="bl_internal"/>
                                        <field name="media_info"/>
                                        <field name="fraud_data"/>
                                        <field name="apu_ppt"/>
                                        <field name="employee_status"/>
                                        <field name="note" widget="text"/>
                                    </group>
                                </sheet>
                            </form> -->
                        </field>
                    </page>
                </xpath>
            </field>
        </record>

        <record id="view_partner_form_inherit" model="ir.ui.view">
            <field name="name">res.partner.form</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="partner_linkaja.view_partner_form_inherit"/>
            <field name="arch" type="xml">
                <xpath expr="//header/button[@name='action_prospective']" position="replace" />
                <xpath expr="//header/button[@name='approve']" position="replace" />
            </field>
        </record>

        <record id="view_res_partner_filter" model="ir.ui.view">
            <field name="name">view.res.partner.filter</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.view_res_partner_filter"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='name']" position="after">
                    <separator/>
                    <filter name="my_approvals" string="My Approvals" domain="[('selected_approver_ids.user_id', '=', uid)]" />
                    <separator/>
                </xpath>
            </field>
        </record>

        <record id="account.res_partner_action_supplier" model="ir.actions.act_window">
            <field name="context">{'search_default_supplier': 1,'res_partner_search_mode': 'supplier', 'default_is_company': True, 'default_supplier_rank': 1, 'default_is_vendor': True, 'vendor_view': True, 'search_default_my_approvals': 1}</field>
        </record>

        <record id="partner_linkaja.res_partner_action_supplier_master" model="ir.actions.act_window">
            <field name="context">{'search_default_supplier': 1,'res_partner_search_mode': 'supplier', 'default_is_company': True, 'default_supplier_rank': 1, 'default_is_vendor': True, 'vendor_view': True, 'search_default_my_approvals': 1}</field>
        </record>
    </data>
</odoo>
