# -*- coding: utf-8 -*-

from odoo import Command, fields, models, api
from odoo.exceptions import ValidationError


class PartnerHierarchyWizard(models.TransientModel):
    _name = 'res.partner.hierarchy.wizard'
    _description = 'Partner Hierarchy Wizard'

    partner_id = fields.Many2one('res.partner', string='Partner')
    from_employee_id = fields.Many2one('hr.employee', string='From Employee')
    to_employee_id = fields.Many2one('hr.employee', string='To Employee')
    note = fields.Char(string='Note')

    employee_domain = fields.Binary(
        compute="_compute_employee_domain"
    )

    @api.depends('partner_id')
    def _compute_employee_domain(self):
        """Compute domain for employee field based on justif"""
        for rec in self:
            domain = [('id', 'in', rec.partner_id.selected_approver_ids.ids)]

            rec.employee_domain = domain

    def action_approve(self):
        self.partner_id.with_context(note=self.note)._approve()

    def action_reject(self):
        self.partner_id.with_context(note=self.note)._reject()

    def action_reassign(self):
        # reassign_to_employee_ids = self.partner_id.approval_reassign_ids.mapped(
        #     'to_employee_id'
        # ).ids
        # reassign_from_employee_ids = self.partner_id.approval_reassign_ids.mapped(
        #     'from_employee_id'
        # ).ids

        # if self.to_employee_id.id in reassign_to_employee_ids:
        #     raise ValidationError('This employee already reassign by other employee!')
        # if self.to_employee_id in reassign_from_employee_ids:
        #     raise ValidationError('This employee cannot assign approval!')

        check_reassign = self.partner_id.approval_reassign_ids.filtered(lambda x: x.to_employee_id == self.from_employee_id)
        if check_reassign:
            check_reassign.write({'to_employee_id': self.to_employee_id.id})

            # check prospective
            check_approval = self.partner_id.approval_history_detail_ids.filtered(lambda x: x.reassign_employee_id == self.from_employee_id)
            if check_approval and self.partner_id._get_target_state() == 'prospective':
                check_approval.write({'reassign_employee_id': self.to_employee_id.id})

            check_approval = self.partner_id.approval_history_detail_ids.filtered(lambda x: x.reassign_employee_id == x.employee_id)
            if check_approval and self.partner_id._get_target_state() == 'prospective':
                check_approval.write({'reassign_employee_id': False})

            # check authorized
            check_approval = self.partner_id.approval_history_detail_authorized_ids.filtered(lambda x: x.reassign_employee_id == self.from_employee_id)
            if check_approval and self.partner_id._get_target_state() != 'prospective':
                check_approval.write({'reassign_employee_id': self.to_employee_id.id})

            check_approval = self.partner_id.approval_history_detail_authorized_ids.filtered(lambda x: x.reassign_employee_id == x.employee_id)
            if check_approval and self.partner_id._get_target_state() != 'prospective':
                check_approval.write({'reassign_employee_id': False})

        else:
            vals = {
                'from_employee_id': self.from_employee_id.id,
                'to_employee_id': self.to_employee_id.id,
            }
        
            self.partner_id.approval_reassign_ids = [(0, 0, vals)]

            # check prospective
            check_approval = self.partner_id.approval_history_detail_ids.filtered(lambda x: x.employee_id == self.from_employee_id)
            if check_approval and self.partner_id._get_target_state() == 'prospective':
                check_approval.write({'reassign_employee_id': self.to_employee_id.id})

            # check authorize
            check_approval = self.partner_id.approval_history_detail_authorized_ids.filtered(lambda x: x.employee_id == self.from_employee_id)
            if check_approval and self.partner_id._get_target_state() != 'prospective':
                check_approval.write({'reassign_employee_id': self.to_employee_id.id})

        self.partner_id._compute_selected_approvers()

        message_vals = {
            'employee_id': self.env.user.employee_id.id,
            'date': fields.Datetime.now(),
            'state': 'reassign',
            'note': f'Reassign approval to {self.to_employee_id.name}',
        }
        self.partner_id.approval_message_ids = [Command.create(message_vals)]

        if self.partner_id._get_target_state() == 'prospective':
            all_approval = self.partner_id.approval_history_ids.mapped('approval_employee_ids.id')
            check_approval = self.partner_id.approval_history_detail_ids.filtered(lambda x: x.reassign_employee_id == self.to_employee_id and x.reassign_employee_state == 'approve')
            if check_approval:
                all_approval += [self.to_employee_id.id]
        else:
            all_approval = self.partner_id.approval_ids.mapped('approval_employee_ids.id')
            check_approval = self.partner_id.approval_history_detail_authorized_ids.filtered(lambda x: x.reassign_employee_id == self.to_employee_id and x.reassign_employee_state == 'approve')
            if check_approval:
                all_approval += [self.to_employee_id.id]

        if all_approval and self.to_employee_id.id in all_approval:
            self.partner_id.with_context({'note': self.note})._approve(approver=self.to_employee_id)

        check_reassign = self.partner_id.approval_reassign_ids.filtered(lambda x: x.from_employee_id == x.to_employee_id)
        if check_reassign:
            check_reassign.unlink()

        # send email
        emails = self.to_employee_id.work_email
        if emails:
            template = self.partner_id._get_info_template()
            message = f"""Reassign approval Supplier name {self.partner_id.name}
            from employee {self.from_employee_id.name}
            """

            self.partner_id.send_email(message, emails, template)
