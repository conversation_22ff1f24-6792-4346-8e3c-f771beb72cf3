# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError
from datetime import datetime
from dateutil import relativedelta
from collections import defaultdict
from odoo.tools import (
    SQL,
)

class AccountMoveInherit(models.Model):
    _inherit = 'account.move'

    reconciled_flag = fields.Selection([
        ('Reconciled', 'Reconciled'),
        ('Unreconciled', 'Unreconciled'),
    ], string='Reconciled Flag')

    def open_payments(self):
        payment_invoice = self.env['account.payment.invoice'].search([('move_id', '=', self.id)])
        payments = payment_invoice.mapped('payment_id')
        return payments._get_records_action(name=_("Payments"))

    def _compute_payment_count(self):
        for invoice in self:
            payment_invoice = self.env['account.payment.invoice'].search([('move_id', '=', invoice.id)])
            payments = payment_invoice.mapped('payment_id')
            invoice.payment_count = len(payments)

    @api.depends('amount_residual', 'move_type', 'state', 'company_id', 'matched_payment_ids.state', 'applied_misc_ids')
    def _compute_payment_state(self):
        stored_ids = tuple(self.ids)
        if stored_ids:
            self.env['account.partial.reconcile'].flush_model()
            self.env['account.payment'].flush_model(['is_matched'])

            queries = []
            for source_field, counterpart_field in (
                ('debit_move_id', 'credit_move_id'),
                ('credit_move_id', 'debit_move_id'),
            ):
                queries.append(SQL('''
                    SELECT
                        source_line.id AS source_line_id,
                        source_line.move_id AS source_move_id,
                        account.account_type AS source_line_account_type,
                        ARRAY_AGG(counterpart_move.move_type) AS counterpart_move_types,
                        COALESCE(BOOL_AND(COALESCE(pay.is_matched, FALSE))
                            FILTER (WHERE counterpart_move.origin_payment_id IS NOT NULL), TRUE) AS all_payments_matched,
                        BOOL_OR(COALESCE(BOOL(pay.id), FALSE)) as has_payment,
                        BOOL_OR(COALESCE(BOOL(counterpart_move.statement_line_id), FALSE)) as has_st_line
                    FROM account_partial_reconcile part
                    JOIN account_move_line source_line ON source_line.id = part.%s
                    JOIN account_account account ON account.id = source_line.account_id
                    JOIN account_move_line counterpart_line ON counterpart_line.id = part.%s
                    JOIN account_move counterpart_move ON counterpart_move.id = counterpart_line.move_id
                    LEFT JOIN account_payment pay ON pay.id = counterpart_move.origin_payment_id
                    WHERE source_line.move_id IN %s AND counterpart_line.move_id != source_line.move_id
                    GROUP BY source_line.id, source_line.move_id, account.account_type
                ''', SQL.identifier(source_field), SQL.identifier(counterpart_field), stored_ids))

            payment_data = defaultdict(list)
            for row in self.env.execute_query_dict(SQL(" UNION ALL ").join(queries)):
                payment_data[row['source_move_id']].append(row)
        else:
            payment_data = {}

        for invoice in self:
            if invoice.move_type not in ['out_invoice', 'in_invoice']:
                if invoice.payment_state == 'invoicing_legacy':
                    # invoicing_legacy state is set via SQL when setting setting field
                    # invoicing_switch_threshold (defined in account_accountant).
                    # The only way of going out of this state is through this setting,
                    # so we don't recompute it here.
                    continue

                currencies = invoice._get_lines_onchange_currency().currency_id
                currency = currencies if len(currencies) == 1 else invoice.company_id.currency_id
                reconciliation_vals = payment_data.get(invoice.id, [])
                payment_state_matters = invoice.is_invoice(True)

                # Restrict on 'receivable'/'payable' lines for invoices/expense entries.
                if payment_state_matters:
                    reconciliation_vals = [x for x in reconciliation_vals if x['source_line_account_type'] in ('asset_receivable', 'liability_payable')]

                new_pmt_state = 'not_paid' if invoice.payment_state != 'blocked' else 'blocked'
                if invoice.state == 'posted':

                    # Posted invoice/expense entry.
                    if payment_state_matters:

                        if currency.is_zero(invoice.amount_residual):
                            if any(x['has_payment'] or x['has_st_line'] for x in reconciliation_vals):

                                # Check if the invoice/expense entry is fully paid or 'in_payment'.
                                if all(x['all_payments_matched'] for x in reconciliation_vals):
                                    new_pmt_state = 'paid'
                                else:
                                    new_pmt_state = invoice._get_invoice_in_payment_state()

                            else:
                                new_pmt_state = 'paid'

                                reverse_move_types = set()
                                for x in reconciliation_vals:
                                    for move_type in x['counterpart_move_types']:
                                        reverse_move_types.add(move_type)

                                in_reverse = (invoice.move_type in ('in_invoice', 'in_receipt')
                                              and (reverse_move_types == {'in_refund'} or reverse_move_types == {'in_refund', 'entry'}))
                                out_reverse = (invoice.move_type in ('out_invoice', 'out_receipt')
                                               and (reverse_move_types == {'out_refund'} or reverse_move_types == {'out_refund', 'entry'}))
                                misc_reverse = (invoice.move_type in ('entry', 'out_refund', 'in_refund')
                                                and reverse_move_types == {'entry'})
                                if in_reverse or out_reverse or misc_reverse:
                                    new_pmt_state = 'reversed'
                        elif invoice.matched_payment_ids.filtered(lambda p: not p.move_id and p.state == 'in_process'):
                            new_pmt_state = invoice._get_invoice_in_payment_state()
                        elif reconciliation_vals:
                            new_pmt_state = 'partial'
                        elif invoice.matched_payment_ids.filtered(lambda p: not p.move_id and p.state == 'paid'):
                            new_pmt_state = invoice._get_invoice_in_payment_state()
                invoice.payment_state = new_pmt_state
            else:
                if invoice.invoice_line_ids:
                    reconciled_amount = 0
                    non_reconciled_amount = 0
                    for applied in invoice.applied_misc_ids:
                        reconciled_line = applied.misc_id.move_id.line_ids.filtered(lambda x: x.reconciled)
                        if reconciled_line:
                            for rec_line in reconciled_line:
                                reconciled_amount += rec_line.amount_currency
                        else:
                            non_reconciled_amount += applied.applied_amount
                    payment_invoice = self.env['account.payment.invoice'].search([('move_id', '=', invoice.id), ('payment_id.state', '=', 'paid')])
                    for payment in payment_invoice:
                        if not payment.payment_id.move_id.line_ids.filtered(lambda x: x.reconciled):
                            non_reconciled_amount = payment.amount
                        else:
                            reconciled_amount += payment.amount
                    invoice.amount_residual = invoice.amount_total_signed - reconciled_amount
                    invoice.amount_residual_signed = invoice.amount_total_signed - reconciled_amount
                    sisa_reconciled_amount = reconciled_amount
                    if invoice.move_type == 'out_invoice':
                        for line in invoice.line_ids.filtered(lambda x: x.debit > 0):
                            if sisa_reconciled_amount > 0:
                                if sisa_reconciled_amount >= line.debit:
                                    sisa_reconciled_amount -= line.debit
                                    line.amount_residual = 0
                                    line.amount_residual_currency = 0
                                else:
                                    line.amount_residual = line.debit - sisa_reconciled_amount
                                    line.amount_residual_currency = line.debit - sisa_reconciled_amount
                                    sisa_reconciled_amount = 0
                    else:
                        for line in invoice.line_ids.filtered(lambda x: x.credit > 0):
                            if sisa_reconciled_amount > 0:
                                if sisa_reconciled_amount >= line.credit:
                                    sisa_reconciled_amount -= line.credit
                                    line.amount_residual = 0
                                    line.amount_residual_currency = 0
                                else:
                                    line.amount_residual = line.credit - sisa_reconciled_amount
                                    line.amount_residual_currency = line.credit - sisa_reconciled_amount
                                    sisa_reconciled_amount = 0
                    # if invoice.applied_misc_ids:
                    #     for line in invoice.line_ids:
                    #         if line.name == line.move_id.name and not line.product_id and not line.tax_line_id:
                    #             line.amount_residual = line.move_id.amount_residual
                    #             line.amount_residual_currency = line.move_id.amount_residual
                    #             if line.amount_currency - (non_reconciled_amount + reconciled_amount) != 0:
                    #                 if line.amount_currency - (non_reconciled_amount + reconciled_amount) == line.amount_currency:
                    #                     line.move_id.payment_state = 'not_paid'
                    #                 else:
                    #                     line.move_id.payment_state = 'partial'
                    #             elif line.amount_currency - (non_reconciled_amount + reconciled_amount) == 0:
                    #                 if line.amount_currency == reconciled_amount:
                    #                     line.move_id.payment_state = 'paid'
                    #                 else:
                    #                     line.move_id.payment_state = 'in_payment'
                    #             # if line.amount_residual == 0 and reconciled_amount == line.amount_currency:
                    #             #     line.move_id.payment_state = 'paid'
                    #             # elif non_reconciled_amount <= line.amount_currency:
                    #             #     line.move_id.payment_state = 'in_payment'
                    #             # elif line.amount_residual > 0:
                    #             #     line.move_id.payment_state = 'partial'
                    #             # elif line.amount_residual == line.move_id.amount_total_signed:
                    #             #     line.move_id.payment_state = 'not_paid'
                    # else:
                    if invoice.amount_total_signed - (non_reconciled_amount + reconciled_amount) != 0:
                        if abs(invoice.amount_total_signed - (non_reconciled_amount + reconciled_amount)) == abs(invoice.amount_total_signed):
                            invoice.payment_state = 'not_paid'
                            invoice.reconciled_flag = 'Unreconciled'
                        else:
                            invoice.payment_state = 'partial'
                            invoice.reconciled_flag = 'Unreconciled'
                    elif invoice.amount_total_signed - (non_reconciled_amount + reconciled_amount) == 0:
                        if invoice.amount_total_signed == reconciled_amount:
                            invoice.payment_state = 'paid'
                            invoice.reconciled_flag = 'Reconciled'
                        else:
                            invoice.payment_state = 'in_payment'
                            invoice.reconciled_flag = 'Unreconciled'
                else:
                    invoice.payment_state = 'not_paid'
                    invoice.reconciled_flag = 'Unreconciled'
