# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError


class InvoicingPeriodLine(models.Model):
    _inherit = 'invoicing.period.line'

    move_type = fields.Selection(
        [('out_invoice', "Customer Invoice"), ('in_invoice', "Vendor Bill")],
        string='Type',
        default='out_invoice',
        related='invoice_period_id.move_type',
    )


class AccountPaymentRegister(models.TransientModel):
    _inherit = 'account.payment.register'

    # Add Period
    period_id = fields.Many2one(
        'invoicing.period.line',
        string='Period',
        compute='_compute_get_period',
        store=True,
    )

    @api.depends('payment_date')
    def _compute_get_period(self):
        for rec in self:
            rec.period_id = False
            if rec.payment_date:
                period = (
                    self.env["invoicing.period.line"]
                    .sudo()
                    .search(
                        [
                            ('date_start', '<=', rec.payment_date),
                            ('date_end', '>=', rec.payment_date),
                            ('move_type', '=', 'out_invoice'),
                        ],
                        limit=1,
                    )
                )
                if period:
                    rec.period_id = period.id

    def action_create_payments(self):
        if self.payment_date and self.period_id.state == 'close':
            raise UserError(
                _('You cannot select a payment date from a closed invoice period.')
            )
        return super().action_create_payments()

    def _create_payments(self):
        result = super()._create_payments()
        active_model = self._context.get('active_model', False)
        active_ids = self._context.get('active_ids', [])

        moves = False
        if active_model == 'account.move':
            moves = self.env['account.move'].browse(active_ids)
        elif active_model == 'account.move.line':
            moves = self.env['account.move.line'].browse(active_ids).move_id

        if moves:
            for move in moves:
                payment_invoice = self.env['account.payment.invoice'].create(
                    {
                        'move_id': move.id,
                        'payment_id': result.id,
                    }
                )
                payment_invoice._onchange_set_values()

        return result

    def _reconcile_payments(self, to_process, edit_mode=False):
        """ Reconcile the payments.

        :param to_process:  A list of python dictionary, one for each payment to create, containing:
                            * create_vals:  The values used for the 'create' method.
                            * to_reconcile: The journal items to perform the reconciliation.
                            * batch:        A python dict containing everything you want about the source journal items
                                            to which a payment will be created (see '_compute_batches').
        :param edit_mode:   Is the wizard in edition mode.
        """
        domain = [
            ('parent_state', '=', 'posted'),
            ('account_type', 'in', self.env['account.payment']._get_valid_payment_account_types()),
            ('reconciled', '=', False),
        ]
        # for vals in to_process:
        #     payment = vals['payment']
        #     payment_lines = payment.move_id.line_ids.filtered_domain(domain)
        #     lines = vals['to_reconcile']
        #     extra_context = {'forced_rate_from_register_payment': vals['rate']} if 'rate' in vals else {}
        #
        #     for account in payment_lines.account_id:
        #         (payment_lines + lines)\
        #             .with_context(**extra_context)\
        #             .filtered_domain([
        #                 ('account_id', '=', account.id),
        #                 ('reconciled', '=', False),
        #                 ('parent_state', '=', 'posted'),
        #             ])\
        #             .reconcile()
        #     lines.move_id.matched_payment_ids += payment


class AccountPayment(models.Model):
    _inherit = 'account.payment'

    def action_validate(self):
        res = super(AccountPayment, self).action_validate()
        for rec in self:
            if rec.state in 'paid':
                for invoice in rec.payment_invoice_ids:
                    invoice.move_id._compute_payment_state()
        return res
