from odoo import _, api, fields, models

class PurchaseOrder(models.Model):
    _inherit = 'purchase.order'

    new_picking_ids = fields.One2many('stock.picking', 'purchase_dua_id', string='New Picking')
    
    def action_open_gr(self):
        # self.ensure_one()
        return {
            'name': _("Good Receipt"),
            'type': 'ir.actions.act_window',
            'res_model': 'stock.picking',
            'view_mode': 'list,form',
            'domain': [('purchase_dua_id', '=', self.id)],
            'context': {'default_picking_type_id': self.picking_type_id.id, 'default_purchase_dua_id': self.id, 'default_partner_id': self.partner_id.id}
       }

class AccountMove(models.Model):
    _inherit = 'account.move'

    def button_cancel(self):
        res = super().button_cancel()

        for bill in self:
            # Only handle vendor bills with GR
            if bill.move_type != 'in_invoice' or not bill.picking_id:
                continue

            picking = bill.picking_id
            picking.action_button_cancel()

        return res
      