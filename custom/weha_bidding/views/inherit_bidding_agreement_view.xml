<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <data>
    
        <record id="ap_agreement_view_form" model="ir.ui.view">
            <field name="name">weha.bidding.agreement.view.form.inherit</field>
            <field name="model">bidding.agreement</field>
            <field name="inherit_id" ref="ap_bidding_agreement.ap_agreement_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//group[1]/group[2]/div" position="replace">
                    <div>
                        <field name="start_date" class="oe_inline" required="1" readonly="state != 'draft'"/> to 
                        <field name="end_date" class="oe_inline" required="1" readonly="state != 'draft'"/>
                    </div>
                </xpath>

                <xpath expr="//field[@name='line_ids']" position="attributes">
                    <attribute name="readonly">0</attribute>
                </xpath>
                <xpath expr="//list/field[@name='unit_price']" position="attributes">
                    <attribute name="readonly">0</attribute>
                </xpath>

            </field>
        </record>
        
    
    </data>
    

</odoo>
