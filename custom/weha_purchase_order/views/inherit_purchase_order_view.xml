<?xml version="1.0" encoding="utf-8"?>
<odoo>

        <record id="purchase_order_form" model="ir.ui.view">
            <field name="name">purchase.order.form</field>
            <field name="model">purchase.order</field>
            <field name="inherit_id" ref="purchase.purchase_order_form"/>
            <field name="arch" type="xml">
                <xpath expr="//header" position="inside">
                    <button name="button_draft" string="Reset" type="object" class="oe_highlight" invisible="new_state in ['canceled', 'reject']"/>
                </xpath>
                <xpath expr="//button[@name='button_done']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
            </field>
        </record>

        <record id="weha_inherit_purchase_order_form" model="ir.ui.view">
            <field name="name">weha.inherit.purchase.order.form</field>
            <field name="model">purchase.order</field>
            <field name="inherit_id" ref="ap_purchase_order.inherit_purchase_order_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='date_purchase']" position="attributes">
                    <attribute name="readonly">new_state != 'draft'</attribute>
                </xpath>
                <xpath expr="//field[@name='transaction_date']" position="attributes">
                    <attribute name="readonly">1</attribute>
                </xpath>
                <xpath expr="//button[@name='action_reject']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='date_purchase']" position="replace">
                    <field name="date_purchase_order" />
                </xpath>
            </field>
        </record>

        <record id="weha_inherit_purchase_order_desc_form" model="ir.ui.view">
            <field name="name">weha.inherit.purchase.order.desc.form</field>
            <field name="model">purchase.order</field>
            <field name="inherit_id" ref="ap_purchase_order.inherit_purchase_order_desc_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='is_po_acknowledge']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>

            </field>
        </record>

        <record id="weha_purchase_order_stock_view_form_inherit" model="ir.ui.view">
            <field name="name">weha.inherit.purchase.order.stock.form</field>
            <field name="model">purchase.order</field>
            <field name="inherit_id" ref="ap_purchase_order.purchase_order_stock_view_form_inherit"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='purchase_assign_id']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                
            </field>
        </record>

        <record id="weha_purchase_order_form_inherit" model="ir.ui.view">
            <field name="name">weha.inherit.purchase.order.order.form</field>
            <field name="model">purchase.order</field>
            <field name="inherit_id" ref="purchase_requisition.purchase_order_form_inherit"/>
            <field name="arch" type="xml">
                <xpath expr="//page[@name='alternative_pos']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                
            </field>
        </record>


        <record id="weha_manual_rate_purchase_form_view" model="ir.ui.view">
            <field name="name">weha.inherit.purchase.order.exchange.form</field>
            <field name="model">purchase.order</field>
            <field name="inherit_id" ref="currency_manual_exchange_rate.manual_rate_purchase_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='manual_currency_rate_active']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                
            </field>
        </record>


        <record id="purchase_order_loa_form" model="ir.ui.view">
            <field name="name">weha.inherit.purchase.order.approval.form</field>
            <field name="model">purchase.order</field>
            <field name="inherit_id" ref="ap_purchase_approval.purchase_order_loa_form"/>
            <field name="arch" type="xml">
                <xpath expr="//button[@name='button_action'][1]" position="attributes">
                    <attribute name="invisible">new_state != 'pending_approval' or not is_current_approver or is_approve</attribute>
                </xpath>
                <xpath expr="//button[@name='button_action'][2]" position="attributes">
                    <attribute name="invisible">new_state != 'pending_approval' or not is_current_approver or is_approve</attribute>
                </xpath>
                <xpath expr="//button[@name='button_action'][3]" position="attributes">
                    <attribute name="invisible">new_state != 'pending_approval' or not is_current_approver or is_approve</attribute>
                </xpath>
                
            </field>
        </record>
        
</odoo>
